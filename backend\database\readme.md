# Database scripts management using db-migrate package

## Overview

This project uses the [db-migrate](https://github.com/db-migrate/node-db-migrate) package to manage MySQL database migrations. db-migrate helps you version, run, and rollback database schema changes in a structured way.

## Prerequisites

- Node.js installed
- MySQL server running
- `db-migrate` and `db-migrate-mysql` packages installed (globally or locally)

Install globally (recommended):

```powershell
npm install -g db-migrate db-migrate-mysql
```

Or install locally in your project:

```powershell
npm install db-migrate db-migrate-mysql --save-dev
```

## Configuration

Create a `database.json` file in the root or `database/` directory with your MySQL connection details:

```json
{
  "dev": {
    "driver": "mysql",
    "host": "localhost",
    "database": "your_db_name",
    "user": "your_db_user",
    "password": "your_db_password"
  }
}
```

## Creating a Migration

To create a new migration script, run:

```powershell
db-migrate create migration_name --sql-file
```

This will generate a new migration file in the `migrations/` folder. Edit the generated SQL files to define your schema changes.

## Running Migrations

To apply all pending migrations:

```powershell
db-migrate up
```

To migrate only one step:

```powershell
db-migrate up --count 1
```

## Rolling Back Migrations

To rollback the last migration:

```powershell
db-migrate down
```

To rollback multiple steps:

```powershell
db-migrate down --count 2
```

## Seeding the Database

If you have seed files, you can run:

```powershell
db-migrate seed:run
```

## Useful Links

- [db-migrate Documentation](https://db-migrate.readthedocs.io/en/latest/)
- [db-migrate-mysql](https://www.npmjs.com/package/db-migrate-mysql)

