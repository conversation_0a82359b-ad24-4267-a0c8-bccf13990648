import { Router } from 'express';
import * as adminController from '../controllers/adminController';
import {
  authenticate,
  requireTenantAdmin,
  requireSuperAdmin,
} from '../middleware/auth';
import {
  idValidation,
  paginationValidation,
  updateUserStatusValidation,
  updateUserRoleValidation,
  reportDateRangeValidation,
} from '../middleware/validation';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Event:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Unique identifier for the event
 *           example: 1
 *         tenant_id:
 *           type: integer
 *           description: ID of the tenant this event belongs to
 *           example: 1
 *         author_id:
 *           type: integer
 *           description: ID of the user who created the event
 *           example: 5
 *         title:
 *           type: string
 *           description: Event title
 *           example: "Alumni Tech Conference 2024"
 *         description:
 *           type: string
 *           nullable: true
 *           description: Detailed description of the event
 *           example: "Annual technology conference bringing together alumni from various tech companies"
 *         image_url:
 *           type: string
 *           nullable: true
 *           description: URL to the event's promotional image
 *           example: "https://example.com/event-image.jpg"
 *         rsvp_link:
 *           type: string
 *           nullable: true
 *           description: Link for event registration/RSVP
 *           example: "https://eventbrite.com/event/123"
 *         start_time:
 *           type: string
 *           format: date-time
 *           description: Event start date and time
 *           example: "2024-03-15T10:00:00Z"
 *         end_time:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           description: Event end date and time
 *           example: "2024-03-15T18:00:00Z"
 *         location:
 *           type: string
 *           nullable: true
 *           description: Event location/venue
 *           example: "Tech Hub, Downtown Campus"
 *         is_public:
 *           type: boolean
 *           description: Whether the event is visible to all users
 *           example: true
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: When the event was created
 *           example: "2024-01-15T08:30:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: When the event was last updated
 *           example: "2024-01-20T14:45:00Z"
 *         author:
 *           $ref: '#/components/schemas/EventAuthor'
 *         tenant:
 *           $ref: '#/components/schemas/EventTenant'
 *     EventAuthor:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 5
 *         full_name:
 *           type: string
 *           example: "John Doe"
 *         email:
 *           type: string
 *           example: "<EMAIL>"
 *     EventTenant:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         name:
 *           type: string
 *           example: "Tech University"
 *     EventsResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Event'
 *         pagination:
 *           $ref: '#/components/schemas/PaginationInfo'
 *     PaginationInfo:
 *       type: object
 *       properties:
 *         page:
 *           type: integer
 *           description: Current page number
 *           example: 1
 *         limit:
 *           type: integer
 *           description: Number of items per page
 *           example: 10
 *         total:
 *           type: integer
 *           description: Total number of items
 *           example: 25
 *         totalPages:
 *           type: integer
 *           description: Total number of pages
 *           example: 3
 *         hasNext:
 *           type: boolean
 *           description: Whether there is a next page
 *           example: true
 *         hasPrev:
 *           type: boolean
 *           description: Whether there is a previous page
 *           example: false
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         error:
 *           type: string
 *           description: Error message
 *           example: "Resource not found"
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: When the error occurred
 *         path:
 *           type: string
 *           description: API endpoint that caused the error
 *           example: "/api/admin/events/123"
 *         method:
 *           type: string
 *           description: HTTP method used
 *           example: "GET"
 *     DeleteEventResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "Event deleted successfully"
 *         data:
 *           type: object
 *           properties:
 *             deletedEvent:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 123
 *                 title:
 *                   type: string
 *                   example: "Alumni Tech Conference 2024"
 *                 start_time:
 *                   type: string
 *                   format: date-time
 *                   example: "2024-03-15T10:00:00Z"
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *       description: JWT token obtained from login endpoint
 *   tags:
 *     - name: Admin
 *       description: General admin operations and dashboard
 *     - name: Admin - User Management
 *       description: User administration operations (view, update status/role)
 *     - name: Admin - Content Management
 *       description: Content moderation operations (posts, jobs, events)
 *     - name: Admin - Reports
 *       description: Analytics and reporting endpoints for admin insights
 */

// All admin routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/admin/dashboard:
 *   get:
 *     summary: Get admin dashboard statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     users:
 *                       type: object
 *                     content:
 *                       type: object
 *                     recentActivity:
 *                       type: object
 *                     tenants:
 *                       type: array
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.get('/dashboard', requireTenantAdmin, adminController.getDashboardStats);

// User Management Routes
/**
 * @swagger
 * /api/admin/users:
 *   get:
 *     summary: Get all users with filtering and pagination
 *     tags: [Admin - User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for name, email, or USN
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [STUDENT, ALUMNUS, TENANT_ADMIN, SUPER_ADMIN]
 *         description: Filter by user role
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, APPROVED, REJECTED, DEACTIVATED]
 *         description: Filter by account status
 *       - in: query
 *         name: tenant_id
 *         schema:
 *           type: integer
 *         description: Filter by tenant (Super Admin only)
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/users',
  requireTenantAdmin,
  paginationValidation,
  adminController.getUsers
);

/**
 * @swagger
 * /api/admin/users/{id}:
 *   get:
 *     summary: Get user by ID
 *     tags: [Admin - User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: User not found
 */
router.get(
  '/users/:id',
  requireTenantAdmin,
  idValidation,
  adminController.getUserById
);

/**
 * @swagger
 * /api/admin/users/{id}/status:
 *   put:
 *     summary: Update user account status
 *     tags: [Admin - User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [PENDING, APPROVED, REJECTED, DEACTIVATED]
 *               reason:
 *                 type: string
 *                 description: Optional reason for status change
 *     responses:
 *       200:
 *         description: User status updated successfully
 *       400:
 *         description: Invalid status
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: User not found
 */
router.put(
  '/users/:id/status',
  requireTenantAdmin,
  idValidation,
  updateUserStatusValidation,
  adminController.updateUserStatus
);

/**
 * @swagger
 * /api/admin/users/{id}/role:
 *   put:
 *     summary: Update user role
 *     tags: [Admin - User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - role
 *             properties:
 *               role:
 *                 type: string
 *                 enum: [STUDENT, ALUMNUS, TENANT_ADMIN, SUPER_ADMIN]
 *     responses:
 *       200:
 *         description: User role updated successfully
 *       400:
 *         description: Invalid role
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: User not found
 */
router.put(
  '/users/:id/role',
  requireTenantAdmin,
  idValidation,
  updateUserRoleValidation,
  adminController.updateUserRole
);

// Content Management Routes
/**
 * @swagger
 * /api/admin/posts:
 *   get:
 *     summary: Get all posts with filtering and pagination
 *     tags: [Admin - Content Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for title, content, or author
 *       - in: query
 *         name: tenant_id
 *         schema:
 *           type: integer
 *         description: Filter by tenant (Super Admin only)
 *     responses:
 *       200:
 *         description: Posts retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/posts',
  requireTenantAdmin,
  paginationValidation,
  adminController.getPosts
);

/**
 * @swagger
 * /api/admin/posts/{id}:
 *   delete:
 *     summary: Delete a post
 *     tags: [Admin - Content Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Post ID
 *     responses:
 *       200:
 *         description: Post deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Post not found
 */
router.delete(
  '/posts/:id',
  requireTenantAdmin,
  idValidation,
  adminController.deletePost
);

/**
 * @swagger
 * /api/admin/jobs:
 *   get:
 *     summary: Get all jobs with filtering and pagination
 *     tags: [Admin - Content Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for title, company, location, or description
 *       - in: query
 *         name: job_type
 *         schema:
 *           type: string
 *           enum: [FULL_TIME, PART_TIME, INTERNSHIP, CONTRACT]
 *         description: Filter by job type
 *       - in: query
 *         name: tenant_id
 *         schema:
 *           type: integer
 *         description: Filter by tenant (Super Admin only)
 *     responses:
 *       200:
 *         description: Jobs retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/jobs',
  requireTenantAdmin,
  paginationValidation,
  adminController.getJobs
);

/**
 * @swagger
 * /api/admin/jobs/{id}:
 *   delete:
 *     summary: Delete a job
 *     tags: [Admin - Content Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Job ID
 *     responses:
 *       200:
 *         description: Job deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Job not found
 */
router.delete(
  '/jobs/:id',
  requireTenantAdmin,
  idValidation,
  adminController.deleteJob
);

/**
 * @swagger
 * /api/admin/events:
 *   get:
 *     summary: Get all events with filtering and pagination
 *     description: Retrieve a paginated list of events with optional filtering. Tenant admins can only see events from their tenant, while super admins can see all events.
 *     tags: [Admin - Content Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *         example: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of events per page
 *         example: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for event title, description, or location
 *         example: "tech conference"
 *       - in: query
 *         name: tenant_id
 *         schema:
 *           type: integer
 *         description: Filter by tenant ID (Super Admin only)
 *         example: 1
 *     responses:
 *       200:
 *         description: Events retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/EventsResponse'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Insufficient permissions - User is not an admin
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get(
  '/events',
  requireTenantAdmin,
  paginationValidation,
  adminController.getEvents
);

/**
 * @swagger
 * /api/admin/events/{id}:
 *   delete:
 *     summary: Delete an event
 *     description: Permanently delete an event from the system. Tenant admins can only delete events from their tenant, while super admins can delete any event. This action cannot be undone.
 *     tags: [Admin - Content Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Unique identifier of the event to delete
 *         example: 123
 *     responses:
 *       200:
 *         description: Event deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DeleteEventResponse'
 *       400:
 *         description: Bad Request - Invalid event ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Insufficient permissions - User is not an admin or trying to delete event from different tenant
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Event not found - The specified event does not exist or user doesn't have access
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.delete(
  '/events/:id',
  requireTenantAdmin,
  idValidation,
  adminController.deleteEvent
);

// Reports Routes
/**
 * @swagger
 * /api/admin/reports/users:
 *   get:
 *     summary: Generate user activity report
 *     tags: [Admin - Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for report (defaults to 30 days ago)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for report (defaults to today)
 *       - in: query
 *         name: tenant_id
 *         schema:
 *           type: integer
 *         description: Filter by tenant (Super Admin only)
 *     responses:
 *       200:
 *         description: User activity report generated successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/reports/users',
  requireTenantAdmin,
  reportDateRangeValidation,
  adminController.getUserActivityReport
);

/**
 * @swagger
 * /api/admin/reports/content:
 *   get:
 *     summary: Generate content activity report
 *     tags: [Admin - Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for report (defaults to 30 days ago)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for report (defaults to today)
 *       - in: query
 *         name: tenant_id
 *         schema:
 *           type: integer
 *         description: Filter by tenant (Super Admin only)
 *     responses:
 *       200:
 *         description: Content activity report generated successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/reports/content',
  requireTenantAdmin,
  reportDateRangeValidation,
  adminController.getContentActivityReport
);

export default router;
