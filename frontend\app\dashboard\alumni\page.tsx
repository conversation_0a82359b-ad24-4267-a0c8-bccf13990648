"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { users as mockUsers } from "@/lib/mock-data";
import { Building, Linkedin, MessageSquare, Search } from "lucide-react";

const getInitials = (name: string) => {
  return name
    .split(" ")
    .map((n) => n[0])
    .join("");
};

const AlumniCard = ({ user }: { user: typeof mockUsers[0] }) => {
  const router = useRouter();

  return (
    <Card className="text-center">
      <CardContent className="p-6">
        <Avatar className="mx-auto h-20 w-20">
          <AvatarImage src={user.avatar} alt={user.name} />
          <AvatarFallback className="text-2xl">{getInitials(user.name)}</AvatarFallback>
        </Avatar>
        <h3 className="mt-4 font-headline text-lg font-semibold">{user.name}</h3>
        <p className="text-sm text-muted-foreground">{user.jobTitle}</p>
        <p className="text-sm text-muted-foreground flex items-center justify-center gap-1.5">
          <Building className="h-3 w-3" />
          {user.company || "N/A"}
        </p>
        <Badge variant="outline" className="mt-2">Batch of {user.batch}</Badge>
        <div className="mt-4 flex justify-center gap-2">
          
        <Button
        variant="outline"
        size="sm"
           onClick={() => router.push("/dashboard/messages")}
>
  <MessageSquare className="mr-2 h-4 w-4" />
  Message
</Button>


          <Button variant="ghost" size="icon" asChild>
            <a href={user.linkedin} target="_blank" rel="noopener noreferrer">
              <Linkedin className="h-5 w-5" />
            </a>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default function AlumniPage() {
  const [search, setSearch] = useState("");
  const [course, setCourse] = useState("");
  const [batch, setBatch] = useState("");
  const [filteredUsers, setFilteredUsers] = useState(mockUsers);

  const applyFilters = () => {
    let result = mockUsers.filter((u) => u.role === "alumnus");

    if (search.trim()) {
      const lower = search.toLowerCase();
      result = result.filter(
        (u) =>
          u.name.toLowerCase().includes(lower) ||
          (u.company?.toLowerCase().includes(lower) ?? false)
      );
    }

    if (course) {
      result = result.filter((u) => u.course === course);
    }

    if (batch) {
      result = result.filter((u) => u.batch === Number(batch));
    }

    setFilteredUsers(result);
  };

  const clearFilters = () => {
    setSearch("");
    setCourse("");
    setBatch("");
    setFilteredUsers(mockUsers.filter((u) => u.role === "alumnus"));
  };

  useEffect(() => {
    applyFilters(); // on mount
  }, []);

  return (
    <div className="container mx-auto">
      <div className="mb-6 flex flex-col items-start gap-4">
        <div>
          <h1 className="font-headline text-3xl font-bold">Find Alumni</h1>
          <p className="text-muted-foreground">
            Network with experienced professionals from your college.
          </p>
        </div>
      </div>

      <Card className="mb-6">
      <CardContent className="p-4 space-y-4">
  {/* Full-width Search bar */}
  <div className="relative w-full">
    <button
      type="button"
      onClick={applyFilters}
      className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"
    >
      <Search className="h-4 w-4" />
    </button>
    <Input
      placeholder="Search by name, company..."
      className="pl-9"
      value={search}
      onChange={(e) => setSearch(e.target.value)}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          applyFilters();
        }
      }}
    />
  </div>

  {/* Row of filters and buttons */}
  <div className="flex flex-col gap-3 md:flex-row md:items-center md:gap-4">
    <Select value={course} onValueChange={setCourse}>
      <SelectTrigger className="w-full md:w-64">
        <SelectValue placeholder="Filter by Course" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="Computer Science">Computer science</SelectItem>
        <SelectItem value="Mechanical Engineering">Mechanical Engineering</SelectItem>
        <SelectItem value="Electronics & Communication">Electronics & Communication</SelectItem>
      </SelectContent>
    </Select>

    <Select value={batch} onValueChange={setBatch}>
      <SelectTrigger className="w-full md:w-64">
        <SelectValue placeholder="Filter by Batch" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="2015">2015</SelectItem>
        <SelectItem value="2012">2012</SelectItem>
        <SelectItem value="2018">2018</SelectItem>
      </SelectContent>
    </Select>

    <div className="flex gap-2 mt-1 md:mt-0">
      <Button onClick={applyFilters}>Apply</Button>
      <Button variant="outline" onClick={clearFilters}>
        Clear
      </Button>
    </div>
  </div>
</CardContent>

      </Card>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {filteredUsers.map((user) => (
          <AlumniCard key={user.id} user={user} />
        ))}
      </div>
    </div>
  );
}