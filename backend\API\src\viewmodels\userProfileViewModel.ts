/**
 * User profile view model
 * Filters sensitive information based on privacy settings
 * showSensitiveInfo parameter controls visibility of sensitive data,
 * if user is following the requester or user fetching his own profile then pass it as true
 */
export function toUserProfileViewModel(user: any, showSensitiveInfo?: boolean) {
  const privacySettings = (user.profile?.privacy_settings as any) || {};
  return {
    id: user.id,
    full_name: user.full_name,
    role: user.role,
    tenant: user.tenant,
    profile: user.profile
      ? {
          ...user.profile,
          linkedin_url:
            showSensitiveInfo || privacySettings.show_linkedin
              ? user.profile.linkedin_url
              : null,
        }
      : null,
    email: showSensitiveInfo || privacySettings.show_email ? user.email : null,
    mobile_number:
      showSensitiveInfo || privacySettings.show_mobile
        ? user.mobile_number
        : null,
    general_posts_count: user._count?.general_posts ?? 0,
    jobs_count: user._count?.jobs ?? 0,
    account_status: user.account_status,
    created_at: user.created_at,
    updated_at: user.updated_at,
  };
}

export function toUserDirectoryViewModel(user: any) {
  return {
    id: user.id,
    full_name: user.full_name,
    email: user.email,
    role: user.role,
    account_status: user.account_status,
    profile: user.profile
      ? {
          current_location: user.profile.current_location,
          company: user.profile.company,
          job_title: user.profile.job_title,
          batch_year: user.profile.batch_year,
          course: user.profile.course
            ? {
                id: user.profile.course.id,
                name: user.profile.course.name,
              }
            : null,
          profile_picture_url: user.profile.profile_picture_url,
        }
      : null,
    tenant: user.tenant
      ? {
          id: user.tenant.id,
          name: user.tenant.name,
          subdomain: user.tenant.subdomain,
        }
      : null,
  };
}

export function toProfileUpdateResponseViewModel(user: any) {
  return {
    success: true,
    message: 'Profile updated successfully',
    data: toUserProfileViewModel(user, true),
  };
}
