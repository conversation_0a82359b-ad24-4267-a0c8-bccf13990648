CREATE TABLE `tenants` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `name` VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    `subdomain` VARCHAR(50) NOT NULL COMMENT 'Unique subdomain for tenant access',
    `logo_url` VARCHAR(255),
    `is_active` TINYINT(1) DEFAULT 1,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_subdomain` (`subdomain`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;