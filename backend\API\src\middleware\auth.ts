import { Request, Response, NextFunction } from 'express';
import { UserRole, UserStatus } from '@prisma/client';
import { AuthUtils } from '../utils/auth';
import { prisma } from '../config/database';
import { createError } from './errorHandler';
import { TokenBlacklistService } from '../services/tokenBlacklistService';

interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
    email: string;
    role: UserRole;
    status: UserStatus;
    id: string;
    tenant_id: number;
  };
}

export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const token = AuthUtils.extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      throw createError('Access token is required', 401);
    }

    const isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(token);
    if (isBlacklisted) {
      throw createError('Token has been invalidated. Please login again.', 401);
    }

    const payload = AuthUtils.verifyAccessToken(token);

    const user = await prisma.user.findUnique({
      where: { id: parseInt(payload.userId, 10) },
      include: {
        tenant: {
          select: {
            is_active: true,
          },
        },
      },
    });

    if (!user || !user.tenant.is_active) {
      throw createError('User not found or tenant inactive', 401);
    }

    const areAllUserTokensBlacklisted =
      await TokenBlacklistService.areAllUserTokensBlacklisted(payload.userId);
    if (areAllUserTokensBlacklisted) {
      throw createError(
        'All user sessions have been invalidated. Please login again.',
        401,
      );
    }

    if (user.account_status === UserStatus.DEACTIVATED) {
      throw createError('Account has been deactivated', 403);
    }

    if (user.account_status === UserStatus.REJECTED) {
      throw createError('Account access has been denied', 403);
    }

    req.user = {
      userId: payload.userId,
      email: user.email,
      role: user.role,
      status: user.account_status,
      id: user.id.toString(),
      tenant_id: user.tenant_id,
    };

    return next();
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'JsonWebTokenError') {
        return next(createError('Invalid token', 401));
      }
      if (error.name === 'TokenExpiredError') {
        return next(createError('Token expired', 401));
      }
    }
    next(error);
  }
};

export const requireApproved = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction,
): void => {
  if (!req.user) {
    return next(createError('Authentication required', 401));
  }

  if (req.user.status !== UserStatus.APPROVED) {
    return next(createError('Account pending approval', 403));
  }

  return next();
};

export const authorize = (...roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(createError('Authentication required', 401));
    }

    if (!roles.includes(req.user.role as UserRole)) {
      return next(createError('Insufficient permissions', 403));
    }

    return next();
  };
};

export const requireTenantAdmin = authorize(
  UserRole.TENANT_ADMIN,
  UserRole.SUPER_ADMIN,
);
export const requireSuperAdmin = authorize(UserRole.SUPER_ADMIN);
export const requireAlumni = authorize(UserRole.ALUMNUS);
export const requireStudent = authorize(UserRole.STUDENT);
export const requireAlumniOrAdmin = authorize(
  UserRole.ALUMNUS,
  UserRole.TENANT_ADMIN,
  UserRole.SUPER_ADMIN,
);
export const requireStudentOrAlumni = authorize(
  UserRole.STUDENT,
  UserRole.ALUMNUS,
);
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction,
) => {
  try {
    const token = AuthUtils.extractTokenFromHeader(req.headers.authorization);

    if (token) {
      const payload = AuthUtils.verifyAccessToken(token);

      const user = await prisma.user.findUnique({
        where: { id: parseInt(payload.userId) },
        include: {
          tenant: {
            select: {
              is_active: true,
            },
          },
        },
      });

      if (
        user &&
        user.account_status === UserStatus.APPROVED &&
        user.tenant.is_active
      ) {
        req.user = {
          userId: payload.userId,
          email: user.email,
          role: user.role,
          status: user.account_status,
          id: user.id.toString(),
          tenant_id: user.tenant_id,
        };
      }
    }
  } catch (error) {
    // Ignore auth errors for optional auth
  }

  next();
};
