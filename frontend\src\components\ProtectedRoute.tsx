'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'STUDENT' | 'ALUMNUS' | 'TENANT_ADMIN' | 'SUPER_ADMIN';
  requiredStatus?: 'PENDING' | 'APPROVED' | 'REJECTED' | 'DEACTIVATED';
  redirectTo?: string;
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredStatus = 'APPROVED',
  redirectTo = '/login',
  fallback = (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p>Loading...</p>
      </div>
    </div>
  ),
}) => {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated || !user) {
        router.push(redirectTo);
        return;
      }

      // Check if user has required role
      if (requiredRole && user.role !== requiredRole) {
        router.push('/unauthorized');
        return;
      }

      // Check if user has required status
      if (requiredStatus && user.account_status !== requiredStatus) {
        if (user.account_status === 'PENDING') {
          router.push('/pending-approval');
          return;
        }
        if (user.account_status === 'REJECTED') {
          router.push('/account-rejected');
          return;
        }
        if (user.account_status === 'DEACTIVATED') {
          router.push('/account-deactivated');
          return;
        }
      }
    }
  }, [isLoading, isAuthenticated, user, requiredRole, requiredStatus, router, redirectTo]);

  // Show loading state while checking authentication
  if (isLoading) {
    return <>{fallback}</>;
  }

  // If not authenticated, don't render children (redirect will happen in useEffect)
  if (!isAuthenticated || !user) {
    return <>{fallback}</>;
  }

  // Check role requirement
  if (requiredRole && user.role !== requiredRole) {
    return <>{fallback}</>;
  }

  // Check status requirement
  if (requiredStatus && user.account_status !== requiredStatus) {
    return <>{fallback}</>;
  }

  // All checks passed, render children
  return <>{children}</>;
};

// Convenience components for common use cases
export const StudentRoute: React.FC<Omit<ProtectedRouteProps, 'requiredRole'>> = (props) => (
  <ProtectedRoute {...props} requiredRole="STUDENT" />
);

export const AlumnusRoute: React.FC<Omit<ProtectedRouteProps, 'requiredRole'>> = (props) => (
  <ProtectedRoute {...props} requiredRole="ALUMNUS" />
);

export const AdminRoute: React.FC<Omit<ProtectedRouteProps, 'requiredRole'>> = (props) => (
  <ProtectedRoute {...props} requiredRole="TENANT_ADMIN" />
);

export const SuperAdminRoute: React.FC<Omit<ProtectedRouteProps, 'requiredRole'>> = (props) => (
  <ProtectedRoute {...props} requiredRole="SUPER_ADMIN" />
);
