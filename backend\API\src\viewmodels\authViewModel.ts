/**
 * Authentication ViewModels for filtering sensitive information in auth responses
 */

export interface AuthUserViewModel {
  id: number;
  full_name: string;
  email: string;
  role: string;
  account_status: string;
  tenant_id: number;
  tenant: {
    id: number;
    name: string;
    subdomain: string;
  };
  created_at: Date;
  updated_at: Date;
}

export interface LoginResponseViewModel {
  success: boolean;
  message: string;
  user: AuthUserViewModel;
  timestamp: string;
}

export interface RegisterResponseViewModel {
  success: boolean;
  message: string;
  user: {
    id: number;
    full_name: string;
    email: string;
    role: string;
    account_status: string;
    tenant_id: number;
  };
  timestamp: string;
}

export interface RefreshTokenResponseViewModel {
  message: string;
  timestamp: string;
}

export interface LogoutResponseViewModel {
  success: boolean;
  message: string;
  timestamp: string;
}

export interface ForgotPasswordResponseViewModel {
  success: boolean;
  message: string;
  timestamp: string;
}

export interface ResetPasswordResponseViewModel {
  success: boolean;
  message: string;
  timestamp: string;
}

/**
 * Transform user data for authentication responses
 * Excludes sensitive information like password hash
 */
export function toAuthUserViewModel(user: any): AuthUserViewModel {
  return {
    id: user.id,
    full_name: user.full_name,
    email: user.email,
    role: user.role,
    account_status: user.account_status,
    tenant_id: user.tenant_id,
    tenant: {
      id: user.tenant.id,
      name: user.tenant.name,
      subdomain: user.tenant.subdomain,
    },
    created_at: user.created_at,
    updated_at: user.updated_at,
  };
}

/**
 * Transform login response
 */
export function toLoginResponseViewModel(user: any): LoginResponseViewModel {
  return {
    success: true,
    message: 'Login successful',
    user: toAuthUserViewModel(user),
    timestamp: new Date().toISOString(),
  };
}

/**
 * Transform register response
 */
export function toRegisterResponseViewModel(user: any): RegisterResponseViewModel {
  return {
    success: true,
    message: 'Registration successful',
    user: {
      id: user.id,
      full_name: user.full_name,
      email: user.email,
      role: user.role,
      account_status: user.account_status,
      tenant_id: user.tenant_id,
    },
    timestamp: new Date().toISOString(),
  };
}

/**
 * Transform refresh token response
 */
export function toRefreshTokenResponseViewModel(): RefreshTokenResponseViewModel {
  return {
    message: 'Token refreshed successfully',
    timestamp: new Date().toISOString(),
  };
}

/**
 * Transform logout response
 */
export function toLogoutResponseViewModel(): LogoutResponseViewModel {
  return {
    success: true,
    message: 'Logout successful',
    timestamp: new Date().toISOString(),
  };
}

/**
 * Transform forgot password response
 */
export function toForgotPasswordResponseViewModel(): ForgotPasswordResponseViewModel {
  return {
    success: true,
    message: 'Password reset email sent successfully',
    timestamp: new Date().toISOString(),
  };
}

/**
 * Transform reset password response
 */
export function toResetPasswordResponseViewModel(): ResetPasswordResponseViewModel {
  return {
    success: true,
    message: 'Password reset successful',
    timestamp: new Date().toISOString(),
  };
}
