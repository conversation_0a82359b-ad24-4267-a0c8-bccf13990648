/**
 * Rate Limiting Middleware Module
 *
 * This module provides rate limiting functionality to protect the IonAlumni API
 * from abuse and ensure fair usage across all clients. It implements different
 * rate limiting strategies for different types of endpoints.
 *
 * Features:
 * - Custom memory store using node-cache for high performance
 * - Environment-specific rate limits (development vs production)
 * - Separate rate limits for authentication endpoints
 * - Cache management utilities for development
 * - Comprehensive logging and monitoring
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-01-01
 */

import rateLimit from 'express-rate-limit';
import { rateLimitCache } from '../config/cache';
import { Logger } from '../services/loggerService';

/**
 * Custom Memory Store for Rate Limiting
 *
 * Implements a high-performance memory store using node-cache for rate limiting.
 * This store provides better performance than the default memory store and
 * includes TTL (Time To Live) management for automatic cleanup.
 *
 * @class NodeCacheStore
 */
class NodeCacheStore {
  private cache = rateLimitCache;

  /**
   * Increments the hit count for a given key
   *
   * @param key - The rate limit key (typically IP address + route)
   * @returns Promise containing hit count and expiration information
   */
  increment(
    key: string,
  ): Promise<{ totalHits: number; timeToExpire?: number; resetTime: Date }> {
    const current = this.cache.get<number>(key) ?? 0;
    const totalHits = current + 1;

    // Initialize new key with TTL
    if (current === 0) {
      const windowMs = parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000', 10);
      this.cache.set(key, totalHits, Math.ceil(windowMs / 1000));
      const resetTime = new Date(Date.now() + windowMs);
      return Promise.resolve({ totalHits, timeToExpire: windowMs, resetTime });
    }
    // Update existing key
    this.cache.set(key, totalHits);
    const ttl = this.cache.getTtl(key);
    const timeToExpire = ttl ? Math.max(0, ttl - Date.now()) : 0;
    const resetTime = ttl ? new Date(ttl) : new Date(Date.now() + 900000);
    return Promise.resolve({ totalHits, timeToExpire, resetTime });
  }

  /**
   * Decrements the hit count for a given key
   * Used when a request is successful and should not count against the limit
   *
   * @param key - The rate limit key
   */
  async decrement(key: string): Promise<void> {
    const current = this.cache.get<number>(key) || 0;
    if (current > 0) {
      this.cache.set(key, current - 1);
    }
  }

  /**
   * Resets the hit count for a given key
   * Used for manual cache invalidation
   *
   * @param key - The rate limit key to reset
   */
  async resetKey(key: string): Promise<void> {
    this.cache.del(key);
  }
}

// =============================================================================
// Store Instances and Utility Functions
// =============================================================================

/**
 * Optimized memory store instance for general rate limiting
 */
const memoryStore = new NodeCacheStore();

/**
 * Development Utility: Clear Rate Limit Cache
 *
 * Provides a way to clear all rate limit data during development.
 * This is useful for testing and development workflows where rate limits
 * might interfere with rapid iteration.
 *
 * @returns {boolean} True if cache was cleared, false if not in development mode
 *
 * @example
 * ```typescript
 * // Clear rate limits during development
 * if (clearRateLimitCache()) {
 *   console.log('Rate limit cache cleared');
 * }
 * ```
 */
export const clearRateLimitCache = () => {
  if (process.env.NODE_ENV === 'development') {
    rateLimitCache.flushAll();
    Logger.info('Rate limit cache cleared for development');
    return true;
  }
  return false;
};

// =============================================================================
// Rate Limiter Configurations
// =============================================================================

/**
 * General Rate Limiter
 *
 * Applies to all API endpoints except authentication routes.
 * Provides protection against general API abuse while allowing
 * reasonable usage for legitimate clients.
 *
 * Configuration:
 * - Window: 15 minutes (configurable via RATE_LIMIT_WINDOW_MS)
 * - Limit: 100 requests per window (configurable via RATE_LIMIT_MAX_REQUESTS)
 * - Scope: Per IP address
 * - Headers: Standard RateLimit-* headers included
 *
 * @example
 * ```typescript
 * // Apply to all routes
 * app.use(rateLimiter);
 * ```
 */
export const rateLimiter = rateLimit({
  store: memoryStore,
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes default
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 100 requests per window
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil(
      parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000', 10) / 1000,
    ),
  },
  standardHeaders: true, // Include RateLimit-* headers for client information
  legacyHeaders: false, // Disable deprecated X-RateLimit-* headers
  handler: (req, res) => {
    // Log security event for monitoring and analysis
    Logger.security('General rate limit exceeded', req.ip, {
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      timestamp: new Date().toISOString(),
    });

    res.status(429).json({
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(
        parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000', 10) / 1000,
      ),
    });
  },
  skip: (req) => {
    // Exempt health check endpoints from rate limiting
    return req.path === '/health' || req.path === '/live';
  },
});

/**
 * Authentication Rate Limiter
 *
 * Provides stricter rate limiting for authentication endpoints to prevent
 * brute force attacks and credential stuffing. Uses environment-specific
 * limits to balance security with development convenience.
 *
 * Configuration:
 * - Development: 50 requests per 5 minutes
 * - Production: 5 requests per 15 minutes
 * - Scope: Per IP address
 * - Separate store: Isolated from general rate limiting
 *
 * Security Features:
 * - Prevents brute force login attempts
 * - Protects against credential stuffing
 * - Separate tracking from general API usage
 * - Enhanced logging for security monitoring
 *
 * @example
 * ```typescript
 * // Apply to authentication routes
 * router.use('/auth', authRateLimiter);
 * ```
 */
export const authRateLimiter = rateLimit({
  store: new NodeCacheStore(), // Dedicated store for authentication rate limiting
  windowMs:
    process.env.NODE_ENV === 'development' ? 5 * 60 * 1000 : 15 * 60 * 1000,
  max: process.env.NODE_ENV === 'development' ? 50 : 5,
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: process.env.NODE_ENV === 'development' ? 300 : 900,
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    const retryAfter = process.env.NODE_ENV === 'development' ? 300 : 900;

    // Enhanced security logging for authentication attempts
    Logger.security('Authentication rate limit exceeded', req.ip, {
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
    });

    res.status(429).json({
      error: 'Too many authentication attempts, please try again later.',
      retryAfter,
    });
  },
  skip: req => {
    // Allow bypassing auth rate limits in development if explicitly disabled
    return (
      process.env.NODE_ENV === 'development' &&
      process.env.DISABLE_AUTH_RATE_LIMIT === 'true'
    );
  },
});

// =============================================================================
// Initialization Logging
// =============================================================================

/**
 * Log rate limiter initialization with current configuration
 * This helps with debugging and monitoring the rate limiting setup
 */
Logger.info('Rate limiters initialized with optimized memory store', {
  environment: process.env.NODE_ENV,
  general: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  },
  authentication: {
    windowMs:
      process.env.NODE_ENV === 'development' ? 5 * 60 * 1000 : 15 * 60 * 1000,
    maxRequests: process.env.NODE_ENV === 'development' ? 50 : 5,
    disabled: process.env.DISABLE_AUTH_RATE_LIMIT === 'true',
  },
});
