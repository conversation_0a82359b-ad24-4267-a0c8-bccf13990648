import cookieParser from 'cookie-parser';
import cors from 'cors';
import dotenv from 'dotenv';
import express from 'express';
import helmet from 'helmet';
import { createServer } from 'http';
import morgan from 'morgan';
import path from 'path';

dotenv.config();

import { config } from './config/config';
import { corsOptions } from './config/cors';
import { initializeSocket } from './config/socket';
import { swaggerSpec, swaggerUi, swaggerUiOptions } from './config/swagger';
import { initializeSocketHandlers } from './handlers/socketHandlers';
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import { clearRateLimitCache, rateLimiter } from './middleware/rateLimiter';
import accountRoutes from './routes/account';
import adminRoutes from './routes/admin';
import adminDashboardRoutes from './routes/admin-dashboard';
import adminUsersRoutes from './routes/admin-users';
import authRoutes from './routes/auth';
import authenticationRoutes from './routes/authentication';
import connectionsRoutes from './routes/connections';
import directoryRoutes from './routes/directory';
import profileRoutes from './routes/profile';
import userRoutes from './routes/user';
import { FileService } from './services/fileService';
import { Logger } from './services/loggerService';

const app = express();
const PORT = parseInt(process.env.PORT ?? '5000', 10);

const initializeServices = (): void => {
  try {
    FileService.initializeCleanup();
    Logger.info('Services initialized successfully');
  } catch (error) {
    Logger.error('Failed to initialize services:', error);
    process.exit(1);
  }
};

const server = createServer(app);
const io = initializeSocket(server);

server.keepAliveTimeout = parseInt(
  process.env.KEEP_ALIVE_TIMEOUT ?? '65000',
  10
);
server.headersTimeout = parseInt(process.env.HEADERS_TIMEOUT ?? '66000', 10);

app.use(
  helmet({
    crossOriginResourcePolicy: { policy: 'cross-origin' },
    contentSecurityPolicy: process.env.NODE_ENV === 'production',
  })
);

app.use(cors(corsOptions));

app.use(
  express.json({
    limit: '10mb',
    strict: true,
    type: ['application/json', 'application/*+json'],
  })
);
app.use(
  express.urlencoded({
    extended: true,
    limit: '10mb',
    parameterLimit: 1000,
  })
);

app.use(cookieParser());

if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

app.use(rateLimiter);

app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Alumni Portal API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    uptime: process.uptime(),
  });
});

app.get('/ready', (_req, res) => {
  res.status(200).json({
    status: 'ready',
    timestamp: new Date().toISOString(),
  });
});

app.get('/live', (_req, res) => {
  res.status(200).json({
    status: 'alive',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
  });
});

if (process.env.NODE_ENV === 'development') {
  app.post('/dev/clear-rate-limits', (_req, res) => {
    const cleared = clearRateLimitCache();
    if (cleared) {
      res.status(200).json({
        success: true,
        message: 'Rate limit cache cleared',
      });
    } else {
      res.status(403).json({
        error: 'This endpoint is only available in development',
      });
    }
  });
}

app.use(
  '/api-docs',
  swaggerUi.serve,
  swaggerUi.setup(swaggerSpec, swaggerUiOptions)
);

app.get('/api-docs.json', (_req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpec);
});

const uploadsPath = path.resolve(__dirname, '../', config.upload.uploadPath);
app.use('/uploads', express.static(uploadsPath));

// Main API routes
app.use('/api/account', accountRoutes);
app.use('/api/auth', authenticationRoutes);
app.use('/api/profile', profileRoutes);
app.use('/api/connections', connectionsRoutes);
app.use('/api/directory', directoryRoutes);

// Admin routes
app.use('/api/admin', adminRoutes);
app.use('/api/admin/users', adminUsersRoutes);
app.use('/api/admin/dashboard', adminDashboardRoutes);

// Legacy routes (for backward compatibility)
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);

app.use(notFoundHandler);
app.use(errorHandler);

const startServer = (): void => {
  try {
    initializeServices();
    initializeSocketHandlers(io);

    server.listen(PORT, () => {
      Logger.info(
        `🚀 Alumni Portal API server running on port http://localhost:${PORT}`
      );
      Logger.info(`📊 Environment: ${process.env.NODE_ENV}`);
      Logger.info('🔌 Socket.IO server initialized and ready');
    });
  } catch (error) {
    Logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();

process.on('SIGTERM', () => {
  Logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    Logger.info('HTTP server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  Logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    Logger.info('HTTP server closed');
    process.exit(0);
  });
});

export default app;
