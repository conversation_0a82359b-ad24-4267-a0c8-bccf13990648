'use client';

import { useState, useRef } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { currentUser as originalCurrentUser, getAuthor, jobs, events, posts } from '@/lib/mock-data';
import { Edit, Trash2 } from 'lucide-react';

const getInitials = (name: string) => name.split(' ').map(n => n[0]).join('');

const EditableField = ({ id, label, value, isEditing, onChange, showError, errorMessage }: {
  id: string,
  label: string,
  value: string | undefined,
  isEditing: boolean,
  onChange: (val: string) => void,
  showError?: boolean,
  errorMessage?: string
}) => (
  <div className="grid gap-2">
    <Label htmlFor={id}>{label}</Label>
    <Input
      id={id}
      value={value ?? ''}
      onChange={(e) => onChange(e.target.value)}
      readOnly={!isEditing}
      aria-invalid={!!showError}
      className={showError ? 'border-red-500' : ''}
    />
    {showError && errorMessage && (
      <p className="text-sm !text-red-500 font-medium">{errorMessage}</p>
    )}
  </div>
);

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false);
  const [user, setUser] = useState({ ...originalCurrentUser });
  const [emailError, setEmailError] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleChange = (field: string, value: string) => {
    setUser(prev => ({ ...prev, [field]: value }));
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        setUser(prev => ({ ...prev, avatar: reader.result as string }));
      };
      reader.readAsDataURL(e.target.files[0]);
    }
  };

  const isAlumnus = user.role === 'alumnus';

  const validateEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.(com|in|org|net|edu|gov|co|io|ai|tech)$/i;
    return regex.test(email);
  };

  const handleSave = () => {
    const isValidEmail = validateEmail(user.email || '');
    if (!isValidEmail) {
      setEmailError('Invalid email format');
      return;
    }
    setEmailError('');
    setIsEditing(false);
  };

  return (
    <div className="container mx-auto max-w-4xl">
      <div className="relative mb-6">
        <div className="h-36 w-full rounded-lg bg-muted">
          <img
            src="https://www.shutterstock.com/image-photo/close-view-businessman-hands-working-600nw-2018014961.jpg"
            className="h-full w-full object-cover rounded-lg"
            alt="Alumni profile background"
          />
        </div>
        <div className="absolute -bottom-12 left-6 flex flex-col items-start">
          <Avatar className="h-24 w-24 border-4 border-background">
            <AvatarImage src={user.avatar} />
            <AvatarFallback className="text-3xl">{getInitials(user.name)}</AvatarFallback>
          </Avatar>
          {isEditing && (
            <>
              <input
                type="file"
                id="avatar-upload"
                accept="image/*"
                onChange={handleAvatarChange}
                ref={fileInputRef}
                className="hidden"
              />
              <label
                htmlFor="avatar-upload"
                className="mt-2 text-sm text-primary underline cursor-pointer"
              >
                {user.avatar ? 'Edit Profile Picture' : 'Upload Profile Picture'}
              </label>
            </>
          )}
        </div>
      </div>

      <div className="mt-16 flex items-end justify-between">
        <div>
          <h1 className="font-headline text-3xl font-bold">{user.name}</h1>
          <p className="text-muted-foreground">{user.course} &middot; Batch of {user.batch}</p>
        </div>
        <Button onClick={() => (isEditing ? handleSave() : setIsEditing(true))}>
          <Edit className="mr-2 h-4 w-4" />{isEditing ? 'Save' : 'Edit Profile'}
        </Button>
      </div>

      <Separator className="my-6" />

      <Tabs defaultValue="profile">
        <TabsList>
          <TabsTrigger value="profile">Profile Details</TabsTrigger>
          <TabsTrigger value="settings">Privacy & Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{isAlumnus ? 'Professional Details' : 'Student Details'}</CardTitle>
              <CardDescription>Manage your details.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <EditableField id="name" label="Name" value={user.name} isEditing={isEditing} onChange={(val) => handleChange('name', val)} />
              <EditableField id="email" label="Email" value={user.email} isEditing={isEditing} onChange={(val) => handleChange('email', val)} showError={!!emailError} errorMessage={emailError} />
              <EditableField id="course" label="Course" value={user.course} isEditing={isEditing} onChange={(val) => handleChange('course', val)} />
              <EditableField id="batch" label="Batch" value={user.batch?.toString()} isEditing={isEditing} onChange={(val) => handleChange('batch', val)} />
              {isAlumnus ? (
                <>
                  <EditableField id="location" label="Location" value={user.location} isEditing={isEditing} onChange={(val) => handleChange('location', val)} />
                  <EditableField id="company" label="Company" value={user.company} isEditing={isEditing} onChange={(val) => handleChange('company', val)} />
                  <EditableField id="jobTitle" label="Job Title" value={user.jobTitle} isEditing={isEditing} onChange={(val) => handleChange('jobTitle', val)} />
                  <EditableField id="linkedin" label="LinkedIn" value={user.linkedin} isEditing={isEditing} onChange={(val) => handleChange('linkedin', val)} />
                </>
              ) : (
                <EditableField id="usn" label="USN" value={user.usn} isEditing={isEditing} onChange={(val) => handleChange('usn', val)} />
              )}
            </CardContent>
          </Card>

          {!isAlumnus && (
            <Card>
              <CardHeader>
                <CardTitle>Followed Alumni</CardTitle>
                <CardDescription>Manage the alumni you follow for updates.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {user.followedAlumni?.map((id: number) => {
                  const alum = getAuthor(id);
                  if (!alum) return null;
                  return (
                    <div key={id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={alum.avatar} />
                          <AvatarFallback>{getInitials(alum.name)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-semibold">{alum.name}</p>
                          <p className="text-sm text-muted-foreground">{alum.jobTitle} at {alum.company}</p>
                        </div>
                      </div>
                      <Button variant="outline">Unfollow</Button>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Privacy Settings</CardTitle>
              <CardDescription>Control what information is visible to others.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between rounded-lg border p-4">
                <div>
                  <Label htmlFor="show-contact" className="font-semibold">Show Contact Number</Label>
                  <p className="text-xs text-muted-foreground">Allow students and alumni to see your phone number.</p>
                </div>
                <Switch id="show-contact" />
              </div>
              <div className="flex items-center justify-between rounded-lg border p-4">
                <div>
                  <Label htmlFor="show-email" className="font-semibold">Show Email Address</Label>
                  <p className="text-xs text-muted-foreground">Allow students and alumni to see your email.</p>
                </div>
                <Switch id="show-email" defaultChecked />
              </div>
              <div className="flex items-center justify-between rounded-lg border p-4">
                <div>
                  <Label htmlFor="show-location" className="font-semibold">Show Current Location</Label>
                  <p className="text-xs text-muted-foreground">Display your city on your public profile.</p>
                </div>
                <Switch id="show-location" defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
