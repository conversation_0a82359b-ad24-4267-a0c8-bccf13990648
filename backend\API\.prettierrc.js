module.exports = {
  // Print width - line length that the printer will wrap on
  printWidth: 80,

  // Tab width - number of spaces per indentation level
  tabWidth: 2,

  // Use tabs instead of spaces
  useTabs: false,

  // Semicolons - print semicolons at the ends of statements
  semi: true,

  // Quotes - use single quotes instead of double quotes
  singleQuote: true,

  // Quote props - change when properties in objects are quoted
  quoteProps: 'as-needed',

  // JSX quotes - use single quotes in JSX
  jsxSingleQuote: true,

  // Trailing commas - print trailing commas wherever possible in multi-line comma-separated syntactic structures
  trailingComma: 'es5',

  // Bracket spacing - print spaces between brackets in object literals
  bracketSpacing: true,

  // Bracket same line - put the > of a multi-line HTML (HTML, JSX, Vue, Angular) element at the end of the last line
  bracketSameLine: false,

  // Arrow function parentheses - include parentheses around a sole arrow function parameter
  arrowParens: 'avoid',

  // Range - format only a segment of a file
  rangeStart: 0,
  rangeEnd: Infinity,

  // Parser - which parser to use (auto-detect based on file extension)
  // parser: undefined,

  // File path - specify the file name to use to infer which parser to use
  // filepath: undefined,

  // Require pragma - only format files that contain a special comment, called a pragma
  requirePragma: false,

  // Insert pragma - insert a special @format marker at the top of files specifying that the file has been formatted with Prettier
  insertPragma: false,

  // Prose wrap - how to wrap prose
  proseWrap: 'preserve',

  // HTML whitespace sensitivity
  htmlWhitespaceSensitivity: 'css',

  // Vue files script and style tags indentation
  vueIndentScriptAndStyle: false,

  // End of line - which end of line characters to apply
  endOfLine: 'lf',

  // Embedded language formatting
  embeddedLanguageFormatting: 'auto',

  // Single attribute per line - enforce single attribute per line in HTML, Vue and JSX
  singleAttributePerLine: false,
};
