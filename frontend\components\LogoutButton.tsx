'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

interface LogoutButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  children?: React.ReactNode;
}

export const LogoutButton: React.FC<LogoutButtonProps> = ({
  variant = 'outline',
  size = 'default',
  className = '',
  children = 'Logout',
}) => {
  const { logout, isLoading } = useAuth();
  const { toast } = useToast();
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    if (isLoggingOut) return;

    setIsLoggingOut(true);

    try {
      await logout();

      toast({
        title: 'Logged out successfully',
        description: 'You have been logged out of your account.',
      });

      // Redirect to login page
      router.push('/login');
    } catch (error: any) {
      console.error('Logout error:', error);
      
      toast({
        variant: 'destructive',
        title: 'Logout failed',
        description: error.message || 'Something went wrong during logout.',
      });
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleLogout}
      disabled={isLoggingOut || isLoading}
    >
      {isLoggingOut ? 'Logging out...' : children}
    </Button>
  );
};
