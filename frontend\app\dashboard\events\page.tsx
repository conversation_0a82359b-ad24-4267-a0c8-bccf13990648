'use client';
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { events as mockEvents, getAuthor, currentUser } from "@/lib/mock-data";
import { Calendar, MapPin, PlusCircle, Search } from "lucide-react";

const EventCard = ({ event }: { event: (typeof mockEvents)[0] }) => {
  const imageMap: Record<string, string> = {
    "Alumni Networking Meetup":
      "https://enterprisealumni.com/hs-fs/hubfs/Website%20images/Blogs/10%20best/The%2010%20Best%20Corporate%20Alumni%20Networks.png?width=1200&height=938&name=The%2010%20Best%20Corporate%20Alumni%20Networks.png",
    "Workshop on Embedded Systems":
      "https://elearn.nptel.ac.in/wp-content/uploads/2023/11/Embedded-rtos--768x409.jpg",
  };

  const imageSrc = imageMap[event.title] || "/events/default.jpg";

  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-0">
        <img
          src={imageSrc}
          alt={event.title}
          className="aspect-video w-full object-cover"
        />
      </CardHeader>
      <CardContent className="p-4">
        <p className="text-sm font-semibold text-accent">
          {new Date(event.date).toLocaleDateString("en-US", {
            weekday: "long",
            month: "long",
            day: "numeric",
          })}
        </p>
        <h3 className="font-headline text-lg font-bold">{event.title}</h3>
        <p className="text-sm text-muted-foreground line-clamp-2">
          {event.description}
        </p>
      </CardContent>
      <CardFooter className="flex justify-between p-4 pt-0">
        <div className="flex items-center text-sm text-muted-foreground">
          <MapPin className="mr-1.5 h-4 w-4" />
          {event.location}
        </div>
        <Button variant="outline" size="sm">
          Details & RSVP
        </Button>
      </CardFooter>
    </Card>
  );
};

export default function EventsPage() {
  const [search, setSearch] = useState("");
  const [date, setDate] = useState("");
  const [filteredEvents, setFilteredEvents] = useState(mockEvents);

  const handleFilter = () => {
    const filtered = mockEvents.filter((event) => {
      const titleMatch =
        event.title.toLowerCase().includes(search.toLowerCase()) ||
        event.description.toLowerCase().includes(search.toLowerCase());

      const dateMatch = date
        ? new Date(event.date).toISOString().split("T")[0] === date
        : true;

      return titleMatch && dateMatch;
    });
    setFilteredEvents(filtered);
  };

  const handleClearFilters = () => {
    setSearch("");
    setDate("");
    setFilteredEvents(mockEvents);
  };

  return (
    <div className="container mx-auto">
      <div className="mb-6 flex flex-col items-start gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="font-headline text-3xl font-bold">Events & Announcements</h1>
          <p className="text-muted-foreground">Connect with the community at upcoming events.</p>
        </div>
        {currentUser.role === "alumnus" && (
          <Button className="bg-accent hover:bg-accent/90">
            <PlusCircle className="mr-2 h-4 w-4" />
            Announce New Event
          </Button>
        )}
      </div>

      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by title, topic..."
                className="pl-9"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <Input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
            />
            <Button onClick={handleFilter}>
              Apply Filters
            </Button>
            <Button variant="outline" onClick={handleClearFilters}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {filteredEvents.length > 0 ? (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredEvents.map((event) => (
            <EventCard key={event.id} event={event} />
          ))}
        </div>
      ) : (
        <p className="text-center text-muted-foreground">
          No events found for the selected criteria.
        </p>
      )}
    </div>
  );
}
