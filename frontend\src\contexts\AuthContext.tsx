'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

export interface User {
  id: number;
  email: string;
  full_name: string;
  role: 'STUDENT' | 'ALUMNUS' | 'TENANT_ADMIN' | 'SUPER_ADMIN';
  account_status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'DEACTIVATED';
  tenant: {
    id: number;
    name: string;
    subdomain: string;
  };
}

export interface AuthState {
  user: User | null;
  accessToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  clearAuth: () => void;
}

export interface RegisterData {
  userType: 'student' | 'alumnus';
  fullName: string;
  email: string;
  password: string;
  mobileNumber: string;
  course: string;
  batch: string;
  usn: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    accessToken: null,
    isLoading: true,
    isAuthenticated: false,
  });

  // Initialize auth state from localStorage on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedToken = localStorage.getItem("accessToken");
        const storedUser = localStorage.getItem("user");

        if (storedToken && storedUser) {
          const user = JSON.parse(storedUser) as User;
          
          // Verify token is still valid
          try {
            const { authApiService } = await import("@/lib/auth-api");
            const verification = await authApiService.verifyToken(storedToken);
            
            if (verification.valid) {
              setAuthState({
                user: verification.user || user,
                accessToken: storedToken,
                isLoading: false,
                isAuthenticated: true,
              });
            } else {
              // Token is invalid, clear auth
              clearAuth();
            }
          } catch (error) {
            // If verification fails, clear auth
            console.error("Token verification failed:", error);
            clearAuth();
          }
        } else {
          setAuthState((prev) => ({
            ...prev,
            isLoading: false,
          }));
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
        clearAuth();
      }
    };

    initializeAuth();
  }, []);

  // Auto-refresh token every 10 minutes
  useEffect(() => {
    if (!authState.isAuthenticated || !authState.accessToken) {
      return;
    }

    const refreshInterval = setInterval(async () => {
      try {
        await refreshToken();
      } catch (error) {
        console.error("Auto token refresh failed:", error);
        // Don't clear auth on refresh failure, let user continue until token expires
      }
    }, 10 * 60 * 1000); // 10 minutes

    return () => clearInterval(refreshInterval);
  }, [authState.isAuthenticated, authState.accessToken]);

  const login = async (email: string, password: string): Promise<void> => {
    setAuthState((prev) => ({ ...prev, isLoading: true }));

    try {
      const { authApiService } = await import("@/lib/auth-api");
      const response = await authApiService.login(email, password);

      // Store tokens and user data
      localStorage.setItem("accessToken", response.accessToken);
      localStorage.setItem("user", JSON.stringify(response.user));

      setAuthState({
        user: response.user,
        accessToken: response.accessToken,
        isLoading: false,
        isAuthenticated: true,
      });
    } catch (error: any) {
      setAuthState((prev) => ({ ...prev, isLoading: false }));
      throw error;
    }
  };

  const register = async (userData: RegisterData): Promise<void> => {
    setAuthState((prev) => ({ ...prev, isLoading: true }));

    try {
      const { authApiService } = await import("@/lib/auth-api");
      const response = await authApiService.register(userData);

      // Registration successful - user might need approval
      setAuthState((prev) => ({ ...prev, isLoading: false }));

      // Don't automatically log in after registration since account might be pending
    } catch (error: any) {
      setAuthState((prev) => ({ ...prev, isLoading: false }));
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    setAuthState((prev) => ({ ...prev, isLoading: true }));

    try {
      const { authApiService } = await import("@/lib/auth-api");
      await authApiService.logout(authState.accessToken || undefined);
    } catch (error) {
      // Continue with logout even if API call fails
      console.error("Logout API call failed:", error);
    } finally {
      clearAuth();
    }
  };

  const refreshToken = async (): Promise<void> => {
    // For now, we'll implement a simple token refresh
    // In a full implementation, you'd use a refresh token
    try {
      const { authApiService } = await import("@/lib/auth-api");
      const currentToken = authState.accessToken;

      if (!currentToken) {
        throw new Error("No access token available");
      }

      const verification = await authApiService.verifyToken(currentToken);

      if (!verification.valid) {
        clearAuth();
        throw new Error("Token is invalid");
      }

      // Token is still valid, update user data if available
      if (verification.user) {
        setAuthState((prev) => ({
          ...prev,
          user: verification.user!,
        }));
      }
    } catch (error) {
      clearAuth();
      throw error;
    }
  };

  const clearAuth = () => {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("user");
    setAuthState({
      user: null,
      accessToken: null,
      isLoading: false,
      isAuthenticated: false,
    });
  };

  const value: AuthContextType = {
    ...authState,
    login,
    register,
    logout,
    refreshToken,
    clearAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
