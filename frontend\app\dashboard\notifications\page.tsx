'use client';

import { useEffect, useState } from 'react';
import { formatDistanceToNow, isToday, isYesterday, isThisWeek } from 'date-fns';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

type NotificationType = 'MESSAGE_RECEIVED' | 'CONNECTION_REQUEST' | 'POST_CREATED' | 'SYSTEM';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  createdAt: string;
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchNotifications = async () => {
      const res = await fetch('/api/notifications');
      const data = await res.json();
      setNotifications(data);
      setLoading(false);
    };
    fetchNotifications();
  }, []);

  const groupNotifications = () => {
    const today: Notification[] = [];
    const yesterday: Notification[] = [];
    const thisWeek: Notification[] = [];
    const earlier: Notification[] = [];

    notifications.forEach((n) => {
      const date = new Date(n.createdAt);
      if (isToday(date)) {
        today.push(n);
      } else if (isYesterday(date)) {
        yesterday.push(n);
      } else if (isThisWeek(date)) {
        thisWeek.push(n);
      } else {
        earlier.push(n);
      }
    });

    return { today, yesterday, thisWeek, earlier };
  };

  const renderGroup = (title: string, items: Notification[]) => {
    if (items.length === 0) return null;
    return (
      <div className="space-y-2">
        <h2 className="text-lg font-semibold text-primary">{title}</h2>
        {items.map((notif) => (
          <Card key={notif.id} className={`border ${notif.isRead ? 'opacity-70' : ''}`}>
            <CardContent className="flex flex-col gap-1 p-4">
              <div className="flex items-center justify-between">
                <h4 className="text-md font-semibold">{notif.title}</h4>
                <Badge variant="outline">{notif.type.replace('_', ' ')}</Badge>
              </div>
              <p className="text-sm text-muted-foreground">{notif.message}</p>
              <span className="text-xs text-gray-400">
                {formatDistanceToNow(new Date(notif.createdAt), { addSuffix: true })}
              </span>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const { today, yesterday, thisWeek, earlier } = groupNotifications();

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold tracking-tight text-primary">Notifications</h1>

      {loading ? (
        <div className="space-y-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-20 w-full rounded-md" />
          ))}
        </div>
      ) : notifications.length === 0 ? (
        <p className="text-center text-muted-foreground">No notifications yet.</p>
      ) : (
        <div className="space-y-6">
          {renderGroup('Today', today)}
          {renderGroup('Yesterday', yesterday)}
          {renderGroup('This Week', thisWeek)}
          {renderGroup('Earlier', earlier)}
        </div>
      )}
    </div>
  );
}
