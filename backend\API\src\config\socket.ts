import { Server as SocketIOServer, ServerOptions } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { corsOptions } from './cors';

// Socket.IO configuration options
export const socketConfig: Partial<ServerOptions> = {
  cors: {
    origin: corsOptions.origin,
    methods: ['GET', 'POST'],
    credentials: true,
  },
  allowEIO3: true,
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
  upgradeTimeout: 30000,
  maxHttpBufferSize: 1e6, // 1MB
  allowRequest: (req, callback) => {
    // Additional security checks can be added here
    callback(null, true);
  },
};

// Socket.IO server instance
let io: SocketIOServer | null = null;

// Initialize Socket.IO server
export const initializeSocket = (server: HTTPServer): SocketIOServer => {
  if (io) {
    return io;
  }

  io = new SocketIOServer(server, socketConfig);

  console.log('🔌 Socket.IO server initialized');
  return io;
};

// Get Socket.IO server instance
export const getSocketIO = (): SocketIOServer => {
  if (!io) {
    throw new Error(
      'Socket.IO server not initialized. Call initializeSocket first.',
    );
  }
  return io;
};

// Socket.IO event names
export const SOCKET_EVENTS = {
  // Connection events
  CONNECTION: 'connection',
  DISCONNECT: 'disconnect',
  CONNECT_ERROR: 'connect_error',

  // Authentication events
  AUTHENTICATE: 'authenticate',
  AUTHENTICATED: 'authenticated',
  AUTHENTICATION_ERROR: 'authentication_error',

  // User presence events
  USER_ONLINE: 'user_online',
  USER_OFFLINE: 'user_offline',
  USER_STATUS_CHANGE: 'user_status_change',
  GET_ONLINE_USERS: 'get_online_users',
  ONLINE_USERS_LIST: 'online_users_list',

  // Messaging events
  SEND_MESSAGE: 'send_message',
  RECEIVE_MESSAGE: 'receive_message',
  MESSAGE_DELIVERED: 'message_delivered',
  MESSAGE_READ: 'message_read',
  TYPING_START: 'typing_start',
  TYPING_STOP: 'typing_stop',
  TYPING_INDICATOR: 'typing_indicator',

  // Notification events
  SEND_NOTIFICATION: 'send_notification',
  RECEIVE_NOTIFICATION: 'receive_notification',
  NOTIFICATION_READ: 'notification_read',
  NOTIFICATION_COUNT: 'notification_count',

  // Follow/Connection events
  FOLLOW_REQUEST: 'follow_request',
  FOLLOW_ACCEPTED: 'follow_accepted',
  FOLLOW_REJECTED: 'follow_rejected',
  CONNECTION_REQUEST: 'connection_request',
  CONNECTION_ACCEPTED: 'connection_accepted',
  CONNECTION_REJECTED: 'connection_rejected',

  // Event-related events
  EVENT_CREATED: 'event_created',
  EVENT_UPDATED: 'event_updated',
  EVENT_DELETED: 'event_deleted',
  EVENT_RSVP: 'event_rsvp',

  // Job-related events
  JOB_POSTED: 'job_posted',
  JOB_UPDATED: 'job_updated',
  JOB_DELETED: 'job_deleted',
  JOB_APPLICATION: 'job_application',

  // Post-related events
  POST_CREATED: 'post_created',
  POST_UPDATED: 'post_updated',
  POST_DELETED: 'post_deleted',
  POST_LIKED: 'post_liked',
  POST_COMMENTED: 'post_commented',

  // Room events
  JOIN_ROOM: 'join_room',
  LEAVE_ROOM: 'leave_room',
  ROOM_MESSAGE: 'room_message',

  // Error events
  ERROR: 'error',
  VALIDATION_ERROR: 'validation_error',
} as const;

// Socket.IO room names
export const SOCKET_ROOMS = {
  // Tenant-specific rooms
  TENANT: (tenantId: number) => `tenant:${tenantId}`,

  // User-specific rooms
  USER: (userId: number) => `user:${userId}`,

  // Chat rooms
  CHAT: (chatId: string) => `chat:${chatId}`,
  PRIVATE_CHAT: (userId1: number, userId2: number) => {
    const sortedIds = [userId1, userId2].sort();
    return `private:${sortedIds[0]}:${sortedIds[1]}`;
  },

  // Event rooms
  EVENT: (eventId: number) => `event:${eventId}`,

  // Job rooms
  JOB: (jobId: number) => `job:${jobId}`,

  // Post rooms
  POST: (postId: number) => `post:${postId}`,

  // General rooms
  NOTIFICATIONS: (userId: number) => `notifications:${userId}`,
  ONLINE_USERS: (tenantId: number) => `online:${tenantId}`,
} as const;

export type SocketEvent = (typeof SOCKET_EVENTS)[keyof typeof SOCKET_EVENTS];
export type SocketRoom = string;
