/**
 * This file replaces the original .eslintignore file
 * All ignore patterns have been moved to the ignorePatterns section in .eslintrc.js
 * This JavaScript version is provided for reference and to prevent TypeScript parsing errors
 */

module.exports = [
  'node_modules/',
  'dist/',
  'build/',
  'out/',
  'coverage/',
  '*.log',
  'logs/',
  'prisma/migrations/',
  'package-lock.json',
  'yarn.lock',
  'pnpm-lock.yaml',
  '.vscode/',
  '.idea/',
  '.DS_Store',
  'Thumbs.db',
  '*.tmp',
  '*.temp',
  '*.d.ts.map',
  '*.js.map',
  '*.js',
  '!.eslintrc.js',
  'uploads/',
  'docs/',
  '.env*',
];
