import { PrismaClient, Prisma } from '@prisma/client';
import { Logger } from '../services/loggerService';

declare global {
  // eslint-disable-next-line no-var, vars-on-top
  var __prisma: PrismaClient | undefined;
  // eslint-disable-next-line no-var, vars-on-top
  var __prismaReadReplica: PrismaClient | undefined;
}

const getDatabaseConfig = (): Prisma.PrismaClientOptions => {
  const isProduction = process.env.NODE_ENV === 'production';

  return {
    log: isProduction
      ? [
          { level: 'error', emit: 'event' },
          { level: 'warn', emit: 'event' },
        ]
      : [
          { level: 'query', emit: 'event' },
          { level: 'error', emit: 'event' },
          { level: 'warn', emit: 'event' },
          { level: 'info', emit: 'event' },
        ],
    errorFormat: isProduction ? 'minimal' : 'pretty',
  };
};

const getReadReplicaConfig = (): Prisma.PrismaClientOptions => {
  const config = getDatabaseConfig();
  const databaseUrl =
    process.env.DATABASE_READ_REPLICA_URL ?? process.env.DATABASE_URL;

  if (!databaseUrl) {
    throw new Error(
      'DATABASE_URL or DATABASE_READ_REPLICA_URL must be set in your environment',
    );
  }

  return config;
};

const createPrismaClient = (
  config: Prisma.PrismaClientOptions,
): PrismaClient => {
  return new PrismaClient(config);
};

const prisma = globalThis.__prisma ?? createPrismaClient(getDatabaseConfig());

const prismaReadReplica =
  globalThis.__prismaReadReplica ??
  (process.env.DATABASE_READ_REPLICA_URL
    ? createPrismaClient(getReadReplicaConfig())
    : prisma);

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
  globalThis.__prismaReadReplica = prismaReadReplica;
}

export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    Logger.error('Database health check failed', error);
    return false;
  }
};

export const checkReadReplicaHealth = async (): Promise<boolean> => {
  if (prismaReadReplica === prisma) {
    return true;
  }

  try {
    await prismaReadReplica.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    Logger.error('Read replica health check failed', error);
    return false;
  }
};

export const getDatabaseMetrics = async (): Promise<Record<
  string,
  unknown
> | null> => {
  try {
    const [userCount, postCount] = await Promise.all([
      prisma.user.count(),
      prisma.generalPost.count(),
    ]);

    return {
      users: userCount,
      posts: postCount,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    Logger.error('Failed to collect database metrics', error);
    return null;
  }
};

export const getConnectionInfo = async (): Promise<Record<
  string,
  unknown
> | null> => {
  try {
    const startTime = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const pingTime = Date.now() - startTime;

    return {
      status: 'connected',
      pingTime: `${pingTime}ms`,
      hasReadReplica: prismaReadReplica !== prisma,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    Logger.error('Failed to get connection info', error);
    return {
      status: 'error',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
    };
  }
};

export const withQueryPerformanceMonitoring = async <T>(
  operation: string,
  queryFn: () => Promise<T>,
): Promise<T> => {
  const startTime = Date.now();

  try {
    const result = await queryFn();
    const duration = Date.now() - startTime;

    if (duration > 1000) {
      Logger.warn(`Slow query: ${operation} (${duration}ms)`);
    }

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error(`Query failed: ${operation} (${duration}ms)`, error);
    throw error;
  }
};

export const getReadClient = (): typeof prismaReadReplica => prismaReadReplica;
export const getWriteClient = (): typeof prisma => prisma;

export const gracefulDatabaseShutdown = async (): Promise<void> => {
  Logger.info('Shutting down database connections...');

  try {
    await Promise.all([
      prisma.$disconnect(),
      prismaReadReplica !== prisma
        ? prismaReadReplica.$disconnect()
        : Promise.resolve(),
    ]);

    Logger.info('Database connections closed successfully');
  } catch (error) {
    Logger.error('Error during database shutdown', error);
    throw error;
  }
};

export const getConnectionPoolStatus = (): Record<string, string> => {
  return {
    active: 'unknown',
    idle: 'unknown',
    total: 'unknown',
    waiting: 'unknown',
    note: 'Prisma manages connection pooling internally.',
  };
};

export { prisma };
const gracefulShutdown = async (signal: string) => {
  console.log(`Received ${signal}, starting graceful shutdown...`);

  try {
    await prisma.$disconnect();
    console.log('Database connections closed successfully');
  } catch (error) {
    console.error('Error during database shutdown:', error);
  }

  process.exit(0);
};

// Handle various shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});
