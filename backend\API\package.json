{"name": "ionalumni", "version": "1.0.0", "description": "", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "npm run lint && npm run type-check && tsc", "start": "node dist/server.js", "prebuild": "npm run clean", "postbuild": "npm run db:generate", "clean": "<PERSON><PERSON><PERSON> dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:reset": "prisma migrate reset", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio --port 7000", "type-check": "tsc --noEmit", "lint": "eslint src/**/*.ts --max-warnings 0", "lint:fix": "eslint src/**/*.ts --fix", "lint:check": "eslint src/**/*.ts", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\"", "code-quality": "npm run lint && npm run format:check && npm run type-check", "code-quality:fix": "npm run lint:fix && npm run format && npm run type-check"}, "repository": {"type": "git", "url": "git+https://github.com/sushmitkumarpatil/IonAlumni.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/sush<PERSON><PERSON><PERSON><PERSON>/IonAlumni/issues"}, "homepage": "https://github.com/sushmitkumarpatil/IonAlumni#readme", "dependencies": {"@bull-board/express": "^6.11.1", "@prisma/client": "^6.12.0", "@types/multer": "^2.0.0", "@types/socket.io": "^3.0.1", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "compression": "^1.8.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "form-data": "^4.0.4", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "node-cache": "^5.1.2", "nodemailer": "^7.0.5", "redis": "^5.6.0", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "ws": "^8.18.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.32.0", "@types/bcryptjs": "^2.4.6", "@types/bull": "^3.15.9", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/express-slow-down": "^1.3.5", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.15", "@types/node-cache": "^4.1.3", "@types/nodemailer": "^6.4.17", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-security": "^3.0.1", "nodemon": "^3.1.10", "prettier": "^3.6.2", "prisma": "^6.12.0", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}