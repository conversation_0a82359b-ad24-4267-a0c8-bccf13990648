{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IonAlumni-Frontend-setup/frontend/src/lib/auth-api.ts"], "sourcesContent": ["import axios from \"axios\";\nimport { User, RegisterData } from \"@/contexts/AuthContext\";\n\n// Create a separate axios instance for auth API calls\nconst authApi = axios.create({\n  baseURL: \"http://localhost:5000/api/auth\",\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\nexport interface LoginResponse {\n  message: string;\n  accessToken: string;\n  user: User;\n  timestamp: string;\n}\n\nexport interface RegisterResponse {\n  success: boolean;\n  message: string;\n  user?: User;\n  timestamp: string;\n}\n\nexport interface LogoutResponse {\n  success: boolean;\n  message: string;\n  timestamp: string;\n}\n\nexport interface RefreshTokenResponse {\n  accessToken: string;\n  message: string;\n  timestamp: string;\n}\n\nexport class AuthApiError extends Error {\n  constructor(message: string, public statusCode?: number, public originalError?: any) {\n    super(message);\n    this.name = \"AuthApiError\";\n  }\n}\n\nexport const authApiService = {\n  /**\n   * Login user with email and password\n   */\n  async login(email: string, password: string): Promise<LoginResponse> {\n    try {\n      const response = await authApi.post<LoginResponse>(\"/login\", {\n        email,\n        password,\n        tenant_id: 20, // Indian Institute of Technology Bombay\n      });\n\n      return response.data;\n    } catch (error: any) {\n      const message = error.response?.data?.error || error.response?.data?.message || \"Login failed. Please try again.\";\n\n      throw new AuthApiError(message, error.response?.status, error);\n    }\n  },\n\n  /**\n   * Register new user\n   */\n  async register(userData: RegisterData): Promise<RegisterResponse> {\n    try {\n      const role = userData.userType === \"student\" ? \"STUDENT\" : \"ALUMNUS\";\n\n      const payload = {\n        tenant_id: 20, // Indian Institute of Technology Bombay\n        full_name: userData.fullName,\n        email: userData.email,\n        password: userData.password,\n        mobile_number: userData.mobileNumber,\n        course_name: userData.course,\n        batch_year: parseInt(userData.batch),\n        usn: userData.usn,\n        role: role,\n      };\n\n      const response = await authApi.post<RegisterResponse>(\"/register\", payload);\n      return response.data;\n    } catch (error: any) {\n      const message =\n        error.response?.data?.error || error.response?.data?.message || \"Registration failed. Please try again.\";\n\n      throw new AuthApiError(message, error.response?.status, error);\n    }\n  },\n\n  /**\n   * Logout user and invalidate token\n   */\n  async logout(accessToken?: string): Promise<LogoutResponse> {\n    try {\n      const headers: any = {};\n      if (accessToken) {\n        headers.Authorization = `Bearer ${accessToken}`;\n      }\n\n      const response = await authApi.post<LogoutResponse>(\n        \"/logout\",\n        {},\n        {\n          headers,\n        }\n      );\n\n      return response.data;\n    } catch (error: any) {\n      const message =\n        error.response?.data?.error || error.response?.data?.message || \"Logout failed. Please try again.\";\n\n      throw new AuthApiError(message, error.response?.status, error);\n    }\n  },\n\n  /**\n   * Refresh access token\n   */\n  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {\n    try {\n      const response = await authApi.post<RefreshTokenResponse>(\"/refresh\", {\n        refreshToken,\n      });\n\n      return response.data;\n    } catch (error: any) {\n      const message =\n        error.response?.data?.error || error.response?.data?.message || \"Token refresh failed. Please login again.\";\n\n      throw new AuthApiError(message, error.response?.status, error);\n    }\n  },\n\n  /**\n   * Verify if current token is valid\n   */\n  async verifyToken(accessToken: string): Promise<{ valid: boolean; user?: User }> {\n    try {\n      const response = await authApi.get(\"/verify\", {\n        headers: {\n          Authorization: `Bearer ${accessToken}`,\n        },\n      });\n\n      return {\n        valid: true,\n        user: response.data.user,\n      };\n    } catch (error: any) {\n      return {\n        valid: false,\n      };\n    }\n  },\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,sDAAsD;AACtD,MAAM,UAAU,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3B,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AA4BO,MAAM,qBAAqB;;;IAChC,YAAY,OAAe,EAAE,AAAO,UAAmB,EAAE,AAAO,aAAmB,CAAE;QACnF,KAAK,CAAC,eAD4B,aAAA,iBAA4B,gBAAA;QAE9D,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,iBAAiB;IAC5B;;GAEC,GACD,MAAM,OAAM,KAAa,EAAE,QAAgB;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAgB,UAAU;gBAC3D;gBACA;gBACA,WAAW;YACb;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;YAEhF,MAAM,IAAI,aAAa,SAAS,MAAM,QAAQ,EAAE,QAAQ;QAC1D;IACF;IAEA;;GAEC,GACD,MAAM,UAAS,QAAsB;QACnC,IAAI;YACF,MAAM,OAAO,SAAS,QAAQ,KAAK,YAAY,YAAY;YAE3D,MAAM,UAAU;gBACd,WAAW;gBACX,WAAW,SAAS,QAAQ;gBAC5B,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,eAAe,SAAS,YAAY;gBACpC,aAAa,SAAS,MAAM;gBAC5B,YAAY,SAAS,SAAS,KAAK;gBACnC,KAAK,SAAS,GAAG;gBACjB,MAAM;YACR;YAEA,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAmB,aAAa;YACnE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,MAAM,UACJ,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;YAElE,MAAM,IAAI,aAAa,SAAS,MAAM,QAAQ,EAAE,QAAQ;QAC1D;IACF;IAEA;;GAEC,GACD,MAAM,QAAO,WAAoB;QAC/B,IAAI;YACF,MAAM,UAAe,CAAC;YACtB,IAAI,aAAa;gBACf,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa;YACjD;YAEA,MAAM,WAAW,MAAM,QAAQ,IAAI,CACjC,WACA,CAAC,GACD;gBACE;YACF;YAGF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,MAAM,UACJ,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;YAElE,MAAM,IAAI,aAAa,SAAS,MAAM,QAAQ,EAAE,QAAQ;QAC1D;IACF;IAEA;;GAEC,GACD,MAAM,cAAa,YAAoB;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAuB,YAAY;gBACpE;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,MAAM,UACJ,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;YAElE,MAAM,IAAI,aAAa,SAAS,MAAM,QAAQ,EAAE,QAAQ;QAC1D;IACF;IAEA;;GAEC,GACD,MAAM,aAAY,WAAmB;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,GAAG,CAAC,WAAW;gBAC5C,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa;gBACxC;YACF;YAEA,OAAO;gBACL,OAAO;gBACP,MAAM,SAAS,IAAI,CAAC,IAAI;YAC1B;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,OAAO;YACT;QACF;IACF;AACF", "debugId": null}}]}