import axios from 'axios';
import { User, RegisterData } from '@/contexts/AuthContext';

// Create a separate axios instance for auth API calls
const authApi = axios.create({
  baseURL: 'http://localhost:5000/api/auth',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface LoginResponse {
  message: string;
  accessToken: string;
  user: User;
  timestamp: string;
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  user?: User;
  timestamp: string;
}

export interface LogoutResponse {
  success: boolean;
  message: string;
  timestamp: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  message: string;
  timestamp: string;
}

export class AuthApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'AuthApiError';
  }
}

export const authApiService = {
  /**
   * Login user with email and password
   */
  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      const response = await authApi.post<LoginResponse>('/login', {
        email,
        password,
        tenant_id: 2, // Hardcoded tenant_id as per existing implementation
      });

      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.error || 
                     error.response?.data?.message || 
                     'Login failed. Please try again.';
      
      throw new AuthApiError(
        message,
        error.response?.status,
        error
      );
    }
  },

  /**
   * Register new user
   */
  async register(userData: RegisterData): Promise<RegisterResponse> {
    try {
      const role = userData.userType === 'student' ? 'STUDENT' : 'ALUMNUS';
      
      const payload = {
        tenant_id: 2, // Hardcoded tenant_id as per existing implementation
        full_name: userData.fullName,
        email: userData.email,
        password: userData.password,
        mobile_number: userData.mobileNumber,
        course_name: userData.course,
        batch_year: parseInt(userData.batch),
        usn: userData.usn,
        role: role,
      };

      const response = await authApi.post<RegisterResponse>('/register', payload);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.error || 
                     error.response?.data?.message || 
                     'Registration failed. Please try again.';
      
      throw new AuthApiError(
        message,
        error.response?.status,
        error
      );
    }
  },

  /**
   * Logout user and invalidate token
   */
  async logout(accessToken?: string): Promise<LogoutResponse> {
    try {
      const headers: any = {};
      if (accessToken) {
        headers.Authorization = `Bearer ${accessToken}`;
      }

      const response = await authApi.post<LogoutResponse>('/logout', {}, {
        headers,
      });

      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.error || 
                     error.response?.data?.message || 
                     'Logout failed. Please try again.';
      
      throw new AuthApiError(
        message,
        error.response?.status,
        error
      );
    }
  },

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    try {
      const response = await authApi.post<RefreshTokenResponse>('/refresh', {
        refreshToken,
      });

      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.error || 
                     error.response?.data?.message || 
                     'Token refresh failed. Please login again.';
      
      throw new AuthApiError(
        message,
        error.response?.status,
        error
      );
    }
  },

  /**
   * Verify if current token is valid
   */
  async verifyToken(accessToken: string): Promise<{ valid: boolean; user?: User }> {
    try {
      const response = await authApi.get('/verify', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return {
        valid: true,
        user: response.data.user,
      };
    } catch (error: any) {
      return {
        valid: false,
      };
    }
  },
};
