CREATE TABLE `users` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `tenant_id` INT NOT NULL COMMENT 'The tenant discriminator column',
    `full_name` VARCHAR(255) NOT NULL,
    `email` VARCHAR(255) NOT NULL,
    `password_hash` VA<PERSON>HAR(255) NOT NULL,
    `mobile_number` VARCHAR(20),
    `usn` VARCHAR(50) NOT NULL COMMENT 'University Seat Number for verification',
    `role` ENUM('STUDENT', 'ALUMNUS', 'TENANT_ADMIN', 'SUPER_ADMIN') NOT NULL,
    `account_status` ENUM('PENDING', 'APPROVED', 'REJECTED', 'DEACTIVATED') NOT NULL DEFAULT 'PENDING',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON>EY (`id`),
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
    UNIQUE KEY `idx_tenant_email` (`tenant_id`, `email`) COMMENT 'Email must be unique within a tenant',
    UNIQUE KEY `idx_tenant_usn` (`tenant_id`, `usn`) COMMENT 'USN must be unique within a tenant'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;