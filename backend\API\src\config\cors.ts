import { CorsOptions } from 'cors';

const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') ?? [
  'http://localhost:3000', // Frontend server
  'http://localhost:3001', // Socket.IO server
  'http://localhost:5000', // API server (for Swagger UI)
  'http://localhost:5173', // Vite default
  'http://localhost:5174', // Vite alternative
];

export const corsOptions: CorsOptions = {
  origin: (origin, callback): void => {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) {
      return callback(null, true);
    }

    // In development, be more permissive for localhost
    if (
      process.env.NODE_ENV === 'development' &&
      origin?.includes('localhost')
    ) {
      return callback(null, true);
    }

    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    } 
      return callback(new Error('Not allowed by CORS'));
    
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Cache-Control',
    'X-Access-Token',
  ],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
  maxAge: 86400, // 24 hours
};
