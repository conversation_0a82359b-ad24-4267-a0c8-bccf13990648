import { Request, Response, NextFunction } from 'express';
import { PrismaClient, UserRole, UserStatus, JobType } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { createError } from '../middleware/errorHandler';
import { Logger } from '../services/loggerService';

interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
    email: string;
    role: UserRole;
    status: UserStatus;
    id: string;
    tenant_id: number;
  };
}

const prisma = new PrismaClient();

// Helper function to validate and parse ID parameter
const validateAndParseId = (
  id: string | undefined,
  paramName: string = 'ID'
): number => {
  if (!id) {
    throw createError(`${paramName} is required`, 400);
  }
  const parsedId = parseInt(id);
  if (isNaN(parsedId)) {
    throw createError(`Invalid ${paramName}`, 400);
  }
  return parsedId;
};

/**
 * Admin Dashboard - Get overview statistics
 */
export const getDashboardStats = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const tenantId = currentUser.tenant_id;
    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;

    // Base where clause - Super admin can see all tenants, Tenant admin only their tenant
    const whereClause = isSuperAdmin ? {} : { tenant_id: tenantId };

    // Get user statistics
    const [
      totalUsers,
      pendingUsers,
      approvedUsers,
      rejectedUsers,
      deactivatedUsers,
      totalAlumni,
      totalStudents,
    ] = await Promise.all([
      prisma.user.count({ where: whereClause }),
      prisma.user.count({
        where: { ...whereClause, account_status: UserStatus.PENDING },
      }),
      prisma.user.count({
        where: { ...whereClause, account_status: UserStatus.APPROVED },
      }),
      prisma.user.count({
        where: { ...whereClause, account_status: UserStatus.REJECTED },
      }),
      prisma.user.count({
        where: { ...whereClause, account_status: UserStatus.DEACTIVATED },
      }),
      prisma.user.count({ where: { ...whereClause, role: UserRole.ALUMNUS } }),
      prisma.user.count({ where: { ...whereClause, role: UserRole.STUDENT } }),
    ]);

    // Get content statistics
    const [totalPosts, totalJobs, totalEvents] = await Promise.all([
      prisma.generalPost.count({ where: whereClause }),
      prisma.job.count({ where: whereClause }),
      prisma.event.count({ where: whereClause }),
    ]);

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [recentUsers, recentPosts, recentJobs, recentEvents] =
      await Promise.all([
        prisma.user.count({
          where: { ...whereClause, created_at: { gte: thirtyDaysAgo } },
        }),
        prisma.generalPost.count({
          where: { ...whereClause, created_at: { gte: thirtyDaysAgo } },
        }),
        prisma.job.count({
          where: { ...whereClause, created_at: { gte: thirtyDaysAgo } },
        }),
        prisma.event.count({
          where: { ...whereClause, created_at: { gte: thirtyDaysAgo } },
        }),
      ]);

    // Get tenant information if super admin
    let tenantStats = null;
    if (isSuperAdmin) {
      const tenants = await prisma.tenant.findMany({
        select: {
          id: true,
          name: true,
          subdomain: true,
          is_active: true,
          _count: {
            select: {
              users: true,
              general_posts: true,
              jobs: true,
              events: true,
            },
          },
        },
        orderBy: { name: 'asc' },
      });
      tenantStats = tenants;
    }

    res.json({
      success: true,
      data: {
        users: {
          total: totalUsers,
          pending: pendingUsers,
          approved: approvedUsers,
          rejected: rejectedUsers,
          deactivated: deactivatedUsers,
          alumni: totalAlumni,
          students: totalStudents,
        },
        content: {
          posts: totalPosts,
          jobs: totalJobs,
          events: totalEvents,
        },
        recentActivity: {
          users: recentUsers,
          posts: recentPosts,
          jobs: recentJobs,
          events: recentEvents,
        },
        tenants: tenantStats,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error getting dashboard stats:', error);
    next(error);
  }
};

/**
 * User Management - Get all users with filtering and pagination
 */
export const getUsers = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const search = req.query.search as string;
    const role = req.query.role as UserRole;
    const status = req.query.status as UserStatus;
    const tenantId = req.query.tenant_id as string;

    const skip = (page - 1) * limit;
    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;

    // Build where clause
    const where: any = {};

    // Super admin can filter by tenant, tenant admin only sees their tenant
    if (isSuperAdmin && tenantId) {
      where.tenant_id = parseInt(tenantId);
    } else if (!isSuperAdmin) {
      where.tenant_id = currentUser.tenant_id;
    }

    if (search) {
      where.OR = [
        { full_name: { contains: search } },
        { email: { contains: search } },
        { usn: { contains: search } },
      ];
    }

    if (role) {
      where.role = role;
    }

    if (status) {
      where.account_status = status;
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          tenant: {
            select: {
              name: true,
              subdomain: true,
            },
          },
          profile: {
            include: {
              course: {
                select: {
                  course_name: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: [{ created_at: 'desc' }],
      }),
      prisma.user.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error getting users:', error);
    next(error);
  }
};

/**
 * User Management - Get single user by ID
 */
export const getUserById = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const { id } = req.params;
    const userId = validateAndParseId(id, 'User ID');

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;
    const whereClause: any = { id: userId };

    // Tenant admin can only see users from their tenant
    if (!isSuperAdmin) {
      whereClause.tenant_id = currentUser.tenant_id;
    }

    const user = await prisma.user.findFirst({
      where: whereClause,
      include: {
        tenant: {
          select: {
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
        _count: {
          select: {
            general_posts: true,
            jobs: true,
            events: true,
            followers: true,
            following: true,
          },
        },
      },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    res.json({
      success: true,
      data: { user },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error getting user by ID:', error);
    next(error);
  }
};

/**
 * User Management - Update user status (approve, reject, deactivate)
 */
export const updateUserStatus = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const { id } = req.params;
    const userId = validateAndParseId(id, 'User ID');
    const { status, reason } = req.body;

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;
    const whereClause: any = { id: userId };

    // Tenant admin can only update users from their tenant
    if (!isSuperAdmin) {
      whereClause.tenant_id = currentUser.tenant_id;
    }

    const targetUser = await prisma.user.findFirst({
      where: whereClause,
      select: { id: true, full_name: true, email: true, account_status: true },
    });

    if (!targetUser) {
      throw createError('User not found', 404);
    }

    // Validate status
    if (!Object.values(UserStatus).includes(status)) {
      throw createError('Invalid status', 400);
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { account_status: status },
      select: {
        id: true,
        full_name: true,
        email: true,
        account_status: true,
        updated_at: true,
      },
    });

    Logger.info(
      `User status updated: ${targetUser.email} -> ${status} by ${req.user.userId}`
    );

    res.json({
      success: true,
      message: `User status updated to ${status}`,
      data: { user: updatedUser },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error updating user status:', error);
    next(error);
  }
};

/**
 * User Management - Update user role
 */
export const updateUserRole = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const { id } = req.params;
    const userId = validateAndParseId(id, 'User ID');
    const { role } = req.body;

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    // Only super admin can change roles to SUPER_ADMIN or TENANT_ADMIN
    if (
      (role === UserRole.SUPER_ADMIN || role === UserRole.TENANT_ADMIN) &&
      currentUser.role !== UserRole.SUPER_ADMIN
    ) {
      throw createError('Insufficient permissions to assign admin roles', 403);
    }

    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;
    const whereClause: any = { id: userId };

    // Tenant admin can only update users from their tenant
    if (!isSuperAdmin) {
      whereClause.tenant_id = currentUser.tenant_id;
    }

    const targetUser = await prisma.user.findFirst({
      where: whereClause,
      select: { id: true, full_name: true, email: true, role: true },
    });

    if (!targetUser) {
      throw createError('User not found', 404);
    }

    // Validate role
    if (!Object.values(UserRole).includes(role)) {
      throw createError('Invalid role', 400);
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { role },
      select: {
        id: true,
        full_name: true,
        email: true,
        role: true,
        updated_at: true,
      },
    });

    Logger.info(
      `User role updated: ${targetUser.email} -> ${role} by ${req.user.userId}`
    );

    res.json({
      success: true,
      message: `User role updated to ${role}`,
      data: { user: updatedUser },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error updating user role:', error);
    next(error);
  }
};

/**
 * Content Management - Get all posts with filtering and pagination
 */
export const getPosts = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const search = req.query.search as string;
    const tenantId = req.query.tenant_id as string;

    const skip = (page - 1) * limit;
    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;

    // Build where clause
    const where: any = {};

    // Super admin can filter by tenant, tenant admin only sees their tenant
    if (isSuperAdmin && tenantId) {
      where.tenant_id = parseInt(tenantId);
    } else if (!isSuperAdmin) {
      where.tenant_id = currentUser.tenant_id;
    }

    if (search) {
      where.OR = [
        { title: { contains: search } },
        { content: { contains: search } },
        { author: { full_name: { contains: search } } },
      ];
    }

    const [posts, total] = await Promise.all([
      prisma.generalPost.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              full_name: true,
              email: true,
              role: true,
            },
          },
          tenant: {
            select: {
              name: true,
              subdomain: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: [{ created_at: 'desc' }],
      }),
      prisma.generalPost.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        posts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error getting posts:', error);
    next(error);
  }
};

/**
 * Content Management - Delete post
 */
export const deletePost = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const { id } = req.params;
    const postId = validateAndParseId(id, 'Post ID');

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;
    const whereClause: any = { id: postId };

    // Tenant admin can only delete posts from their tenant
    if (!isSuperAdmin) {
      whereClause.tenant_id = currentUser.tenant_id;
    }

    const post = await prisma.generalPost.findFirst({
      where: whereClause,
      select: { id: true, title: true, author_id: true },
    });

    if (!post) {
      throw createError('Post not found', 404);
    }

    await prisma.generalPost.delete({
      where: { id: postId },
    });

    Logger.info(
      `Post deleted: ${post.title} (ID: ${post.id}) by admin ${req.user.userId}`
    );

    res.json({
      success: true,
      message: 'Post deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error deleting post:', error);
    next(error);
  }
};

/**
 * Content Management - Get all jobs with filtering and pagination
 */
export const getJobs = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const search = req.query.search as string;
    const jobType = req.query.job_type as JobType;
    const tenantId = req.query.tenant_id as string;

    const skip = (page - 1) * limit;
    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;

    // Build where clause
    const where: any = {};

    // Super admin can filter by tenant, tenant admin only sees their tenant
    if (isSuperAdmin && tenantId) {
      where.tenant_id = parseInt(tenantId);
    } else if (!isSuperAdmin) {
      where.tenant_id = currentUser.tenant_id;
    }

    if (search) {
      where.OR = [
        { title: { contains: search } },
        { company_name: { contains: search } },
        { location: { contains: search } },
        { description: { contains: search } },
      ];
    }

    if (jobType) {
      where.job_type = jobType;
    }

    const [jobs, total] = await Promise.all([
      prisma.job.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              full_name: true,
              email: true,
              role: true,
            },
          },
          tenant: {
            select: {
              name: true,
              subdomain: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: [{ created_at: 'desc' }],
      }),
      prisma.job.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        jobs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error getting jobs:', error);
    next(error);
  }
};

/**
 * Content Management - Delete job
 */
export const deleteJob = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const { id } = req.params;
    const jobId = validateAndParseId(id, 'Job ID');

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;
    const whereClause: any = { id: jobId };

    // Tenant admin can only delete jobs from their tenant
    if (!isSuperAdmin) {
      whereClause.tenant_id = currentUser.tenant_id;
    }

    const job = await prisma.job.findFirst({
      where: whereClause,
      select: { id: true, title: true, company_name: true },
    });

    if (!job) {
      throw createError('Job not found', 404);
    }

    await prisma.job.delete({
      where: { id: jobId },
    });

    Logger.info(
      `Job deleted: ${job.title} at ${job.company_name} (ID: ${job.id}) by admin ${req.user.userId}`
    );

    res.json({
      success: true,
      message: 'Job deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error deleting job:', error);
    next(error);
  }
};

/**
 * Content Management - Get all events with filtering and pagination
 */
export const getEvents = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const search = req.query.search as string;
    const tenantId = req.query.tenant_id as string;

    const skip = (page - 1) * limit;
    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;

    // Build where clause
    const where: any = {};

    // Super admin can filter by tenant, tenant admin only sees their tenant
    if (isSuperAdmin && tenantId) {
      where.tenant_id = parseInt(tenantId);
    } else if (!isSuperAdmin) {
      where.tenant_id = currentUser.tenant_id;
    }

    if (search) {
      where.OR = [
        { title: { contains: search } },
        { description: { contains: search } },
        { location: { contains: search } },
      ];
    }

    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              full_name: true,
              email: true,
              role: true,
            },
          },
          tenant: {
            select: {
              name: true,
              subdomain: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: [{ start_time: 'desc' }],
      }),
      prisma.event.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        events,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error getting events:', error);
    next(error);
  }
};

/**
 * Content Management - Delete event
 */
export const deleteEvent = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const { id } = req.params;
    const eventId = validateAndParseId(id, 'Event ID');

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;
    const whereClause: any = { id: eventId };

    // Tenant admin can only delete events from their tenant
    if (!isSuperAdmin) {
      whereClause.tenant_id = currentUser.tenant_id;
    }

    const event = await prisma.event.findFirst({
      where: whereClause,
      select: { id: true, title: true, start_time: true },
    });

    if (!event) {
      throw createError('Event not found', 404);
    }

    await prisma.event.delete({
      where: { id: eventId },
    });

    Logger.info(
      `Event deleted: ${event.title} (ID: ${event.id}) by admin ${req.user.userId}`
    );

    res.json({
      success: true,
      message: 'Event deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error deleting event:', error);
    next(error);
  }
};

/**
 * Reports - Generate user activity report
 */
export const getUserActivityReport = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const { start_date, end_date, tenant_id } = req.query;
    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;

    // Date range validation
    const startDate = start_date
      ? new Date(start_date as string)
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = end_date ? new Date(end_date as string) : new Date();

    // Build where clause
    const whereClause: any = {
      created_at: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Super admin can filter by tenant, tenant admin only sees their tenant
    if (isSuperAdmin && tenant_id) {
      whereClause.tenant_id = parseInt(tenant_id as string);
    } else if (!isSuperAdmin) {
      whereClause.tenant_id = currentUser.tenant_id;
    }

    // Get user registrations by day
    const userRegistrations = await prisma.user.groupBy({
      by: ['created_at'],
      where: whereClause,
      _count: {
        id: true,
      },
      orderBy: {
        created_at: 'asc',
      },
    });

    // Get user status breakdown
    const userStatusBreakdown = await prisma.user.groupBy({
      by: ['account_status'],
      where: whereClause,
      _count: {
        id: true,
      },
    });

    // Get user role breakdown
    const userRoleBreakdown = await prisma.user.groupBy({
      by: ['role'],
      where: whereClause,
      _count: {
        id: true,
      },
    });

    // Get most active users (by posts, jobs, events created)
    const activeUsers = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        full_name: true,
        email: true,
        role: true,
        _count: {
          select: {
            general_posts: true,
            jobs: true,
            events: true,
          },
        },
      },
      orderBy: [
        {
          general_posts: {
            _count: 'desc',
          },
        },
      ],
      take: 10,
    });

    res.json({
      success: true,
      data: {
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
        },
        registrations: userRegistrations,
        statusBreakdown: userStatusBreakdown,
        roleBreakdown: userRoleBreakdown,
        activeUsers,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error generating user activity report:', error);
    next(error);
  }
};

/**
 * Reports - Generate content activity report
 */
export const getContentActivityReport = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true, role: true },
    });

    if (!currentUser) {
      throw createError('User not found', 404);
    }

    const { start_date, end_date, tenant_id } = req.query;
    const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;

    // Date range validation
    const startDate = start_date
      ? new Date(start_date as string)
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = end_date ? new Date(end_date as string) : new Date();

    // Build where clause
    const whereClause: any = {
      created_at: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Super admin can filter by tenant, tenant admin only sees their tenant
    if (isSuperAdmin && tenant_id) {
      whereClause.tenant_id = parseInt(tenant_id as string);
    } else if (!isSuperAdmin) {
      whereClause.tenant_id = currentUser.tenant_id;
    }

    // Get content creation statistics
    const [postsCount, jobsCount, eventsCount] = await Promise.all([
      prisma.generalPost.count({ where: whereClause }),
      prisma.job.count({ where: whereClause }),
      prisma.event.count({ where: whereClause }),
    ]);

    // Get posts by day
    const postsByDay = await prisma.generalPost.groupBy({
      by: ['created_at'],
      where: whereClause,
      _count: {
        id: true,
      },
      orderBy: {
        created_at: 'asc',
      },
    });

    // Get jobs by type
    const jobsByType = await prisma.job.groupBy({
      by: ['job_type'],
      where: whereClause,
      _count: {
        id: true,
      },
    });

    // Get top content creators
    const topCreators = await prisma.user.findMany({
      where:
        isSuperAdmin && tenant_id
          ? { tenant_id: parseInt(tenant_id as string) }
          : !isSuperAdmin
            ? { tenant_id: currentUser.tenant_id }
            : {},
      select: {
        id: true,
        full_name: true,
        email: true,
        role: true,
        _count: {
          select: {
            general_posts: {
              where: {
                created_at: {
                  gte: startDate,
                  lte: endDate,
                },
              },
            },
            jobs: {
              where: {
                created_at: {
                  gte: startDate,
                  lte: endDate,
                },
              },
            },
            events: {
              where: {
                created_at: {
                  gte: startDate,
                  lte: endDate,
                },
              },
            },
          },
        },
      },
      orderBy: [
        {
          general_posts: {
            _count: 'desc',
          },
        },
      ],
      take: 10,
    });

    res.json({
      success: true,
      data: {
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
        },
        summary: {
          posts: postsCount,
          jobs: jobsCount,
          events: eventsCount,
          total: postsCount + jobsCount + eventsCount,
        },
        postsByDay,
        jobsByType,
        topCreators,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    Logger.error('Error generating content activity report:', error);
    next(error);
  }
};
