# **App Name**: AlmaConnect

## Core Features:

- User Authentication and Verification: Secure registration and login for alumni and students with USN verification. Includes profile completion prompts post-approval.
- Profile Management: Customizable alumni and student profiles with editable fields, privacy settings, and content management for alumni.
- Job/Internship Board: Alumni can create and manage job postings and internship opportunities, including required fields, optional details, and visibility toggles.
- Job Search: Students can search and filter job postings by course, industry, company, location, and job type. Job posts tagged with the student's current course are prominently displayed on their dashboard.
- Events & Announcements: Alumni can announce events, including event details and registration links. Students can discover events on their dashboard and a dedicated events section.
- Direct Messaging & Connection Tool: Networking via direct messaging for students to connect with alumni. Prominent "Message" button on profiles. Tool analyzes chat messages and suggests helpful connections based on student interests, alumni expertise, and engagement history.
- Notifications: Notifications for new job postings, events, messages, and connection requests via in-app notifications and optional email subscriptions.

## Style Guidelines:

- Primary color: Deep Blue (#1E3A8A) to convey trust, professionalism, and connection to academia.
- Background color: Light gray (#F9FAFB) for a clean, modern, and accessible layout, which creates enough contrast to improve readability, due to a dark color scheme.
- Accent color: Orange (#EA580C) to highlight key actions and calls to action to encourage user engagement.
- Font pairing: 'Space Grotesk' (sans-serif) for headings, to give a computerized, techy look; and 'Inter' (sans-serif) for body, for a machined, objective, neutral style. 
- Modern, professional icons for navigation and actions. Consistent style and clear representation.
- Clean, intuitive layout using 'Shadcn UI' components for responsiveness across devices. Dashboard feed for personalized content.
- Subtle animations for transitions and feedback, such as loading indicators and message confirmations. Understated to provide clarity, and without unnecessary distractions.