/**
 * Admin ViewModels for filtering sensitive information in admin responses
 */

export interface AdminUserViewModel {
  id: number;
  full_name: string;
  email: string;
  role: string;
  account_status: string;
  tenant_id: number;
  tenant?: {
    id: number;
    name: string;
    subdomain: string;
  };
  profile?:
    | {
        id: number;
        current_location?: string | undefined;
        company?: string | undefined;
        job_title?: string | undefined;
        batch_year?: number | undefined;
        course?:
          | {
              course_name: string;
            }
          | undefined;
      }
    | undefined;
  created_at: Date;
  updated_at: Date;
}

export interface AdminDashboardViewModel {
  users: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    deactivated: number;
    alumni: number;
    students: number;
  };
  content: {
    posts: number;
    jobs: number;
    events: number;
  };
  recentActivity: {
    users: number;
    posts: number;
    jobs: number;
    events: number;
  };
  tenants?: Array<{
    id: number;
    name: string;
    subdomain: string;
    is_active: boolean;
    _count: {
      users: number;
      general_posts: number;
      jobs: number;
      events: number;
    };
  }>;
}

export interface AdminUserActivityViewModel {
  dateRange: {
    start: string;
    end: string;
  };
  registrations: Array<{
    date: string;
    count: number;
  }>;
  statusBreakdown: Array<{
    account_status: string;
    _count: {
      id: number;
    };
  }>;
  roleBreakdown: Array<{
    role: string;
    _count: {
      id: number;
    };
  }>;
  activeUsers: Array<{
    id: number;
    full_name: string;
    email: string;
    role: string;
    _count: {
      general_posts: number;
      jobs: number;
      events: number;
    };
  }>;
}

/**
 * Transform user data for admin view
 * Includes all necessary information for admin operations
 */
export function toAdminUserViewModel(user: any): AdminUserViewModel {
  const result: AdminUserViewModel = {
    id: user.id,
    full_name: user.full_name,
    email: user.email,
    role: user.role,
    account_status: user.account_status,
    tenant_id: user.tenant_id,
    created_at: user.created_at,
    updated_at: user.updated_at,
  };

  // Add optional fields
  if (user.tenant) {
    result.tenant = {
      id: user.tenant.id,
      name: user.tenant.name,
      subdomain: user.tenant.subdomain,
    };
  }

  if (user.profile) {
    result.profile = {
      id: Number(user.profile.id),
      current_location: user.profile.current_location as string | undefined,
      company: user.profile.company as string | undefined,
      job_title: user.profile.job_title as string | undefined,
      batch_year: user.profile.batch_year
        ? Number(user.profile.batch_year)
        : undefined,
      course: user.profile.course
        ? {
            course_name: user.profile.course.course_name as string,
          }
        : undefined,
    };
  }

  return result;
}

/**
 * Transform dashboard data for admin view
 */
export function toAdminDashboardViewModel(data: any): AdminDashboardViewModel {
  return {
    users: {
      total: data.totalUsers || 0,
      pending: data.pendingUsers || 0,
      approved: data.approvedUsers || 0,
      rejected: data.rejectedUsers || 0,
      deactivated: data.deactivatedUsers || 0,
      alumni: data.totalAlumni || 0,
      students: data.totalStudents || 0,
    },
    content: {
      posts: data.totalPosts || 0,
      jobs: data.totalJobs || 0,
      events: data.totalEvents || 0,
    },
    recentActivity: {
      users: data.recentUsers || 0,
      posts: data.recentPosts || 0,
      jobs: data.recentJobs || 0,
      events: data.recentEvents || 0,
    },
    tenants: data.tenantStats || undefined,
  };
}

/**
 * Transform user activity report data for admin view
 */
export function toAdminUserActivityViewModel(
  data: any
): AdminUserActivityViewModel {
  return {
    dateRange: data.dateRange,
    registrations: data.registrations || [],
    statusBreakdown: data.statusBreakdown || [],
    roleBreakdown: data.roleBreakdown || [],
    activeUsers: data.activeUsers || [],
  };
}
