import { Router } from 'express';
import { authenticate, requireApproved } from '../middleware/auth';
import { Logger } from '../services/loggerService';
import { NotificationType } from '../types/socket';
import { SocketUtils } from '../utils/socketUtils';

const router = Router();

// All routes require authentication
router.use(authenticate);

// Get online users for the current tenant
router.get('/online-users', requireApproved, async (req, res) => {
  try {
    const tenantId = req.user?.tenant_id;
    if (!tenantId) {
      return res.status(400).json({ error: 'Tenant ID not found' });
    }

    const onlineUsers = SocketUtils.getOnlineUsers(tenantId);
    return res.json({
      success: true,
      data: onlineUsers,
      count: onlineUsers.length,
    });
  } catch (error) {
    Logger.error('Error getting online users:', error);
    return res.status(500).json({ error: 'Failed to get online users' });
  }
});

// Check if a specific user is online
router.get('/user/:userId/online', requireApproved, async (req, res) => {
  try {
    const userId = parseInt(req.params.userId!);
    if (isNaN(userId)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    const isOnline = SocketUtils.isUserOnline(userId);
    return res.json({
      success: true,
      data: { userId, isOnline },
    });
  } catch (error) {
    Logger.error('Error checking user online status:', error);
    return res
      .status(500)
      .json({ error: 'Failed to check user online status' });
  }
});

// Send a test notification to current user
router.post('/test-notification', requireApproved, async (req, res) => {
  try {
    const userId = req.user?.id;
    const tenantId = req.user?.tenant_id;
    const { title, message, type = 'SYSTEM' } = req.body;

    if (!userId || !tenantId) {
      return res.status(400).json({ error: 'User or tenant ID not found' });
    }

    if (!title || !message) {
      return res.status(400).json({ error: 'Title and message are required' });
    }

    await SocketUtils.sendCustomNotification(
      parseInt(userId),
      tenantId,
      type as NotificationType,
      title,
      message,
      {
        test: true,
        timestamp: new Date(),
      },
    );

    return res.json({
      success: true,
      message: 'Test notification sent',
    });
  } catch (error) {
    Logger.error('Error sending test notification:', error);
    return res.status(500).json({ error: 'Failed to send test notification' });
  }
});

// Send notification to multiple users (admin only)
router.post('/broadcast-notification', requireApproved, async (req, res) => {
  try {
    const currentUser = req.user;
    const { userIds, title, message, type = 'SYSTEM' } = req.body;

    if (
      !currentUser ||
      !['TENANT_ADMIN', 'SUPER_ADMIN'].includes(currentUser.role)
    ) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ error: 'User IDs array is required' });
    }

    if (!title || !message) {
      return res.status(400).json({ error: 'Title and message are required' });
    }

    await SocketUtils.sendCustomNotificationToUsers(
      userIds,
      currentUser.tenant_id,
      type as NotificationType,
      title,
      message,
      { broadcastBy: currentUser.userId, timestamp: new Date() },
    );

    return res.json({
      success: true,
      message: `Notification sent to ${userIds.length} users`,
    });
  } catch (error) {
    Logger.error('Error broadcasting notification:', error);
    return res.status(500).json({ error: 'Failed to broadcast notification' });
  }
});

// Send system notification to entire tenant (admin only)
router.post('/system-notification', requireApproved, async (req, res) => {
  try {
    const currentUser = req.user;
    const { title, message } = req.body;

    if (
      !currentUser ||
      !['TENANT_ADMIN', 'SUPER_ADMIN'].includes(currentUser.role)
    ) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    if (!title || !message) {
      return res.status(400).json({ error: 'Title and message are required' });
    }

    await SocketUtils.triggerSystemNotification(
      currentUser.tenant_id,
      title,
      message,
      {
        sentBy: currentUser.userId,
        timestamp: new Date(),
      },
    );

    return res.json({
      success: true,
      message: 'System notification sent to all users in tenant',
    });
  } catch (error) {
    Logger.error('Error sending system notification:', error);
    return res
      .status(500)
      .json({ error: 'Failed to send system notification' });
  }
});

// Get server statistics (admin only)
router.get('/stats', requireApproved, async (req, res) => {
  try {
    const currentUser = req.user;

    if (
      !currentUser ||
      !['TENANT_ADMIN', 'SUPER_ADMIN'].includes(currentUser.role)
    ) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    const stats = SocketUtils.getServerStats();
    return res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    Logger.error('Error getting server stats:', error);
    return res.status(500).json({ error: 'Failed to get server statistics' });
  }
});

// Force disconnect a user (admin only)
router.post('/disconnect-user', requireApproved, async (req, res) => {
  try {
    const currentUser = req.user;
    const { userId, reason } = req.body;

    if (
      !currentUser ||
      !['TENANT_ADMIN', 'SUPER_ADMIN'].includes(currentUser.role)
    ) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    await SocketUtils.forceDisconnectUser(
      userId,
      reason || `Disconnected by ${currentUser.userId}`,
    );

    return res.json({
      success: true,
      message: `User ${userId} has been disconnected`,
    });
  } catch (error) {
    Logger.error('Error disconnecting user:', error);
    return res.status(500).json({ error: 'Failed to disconnect user' });
  }
});

// Get room members (for debugging)
router.get('/room/:roomId/members', requireApproved, async (req, res) => {
  try {
    const currentUser = req.user;
    const { roomId } = req.params;

    if (
      !currentUser ||
      !['TENANT_ADMIN', 'SUPER_ADMIN'].includes(currentUser.role)
    ) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    const members = await SocketUtils.getRoomMembers(roomId!);
    return res.json({
      success: true,
      data: { roomId, members, count: members.length },
    });
  } catch (error) {
    Logger.error('Error getting room members:', error);
    return res.status(500).json({ error: 'Failed to get room members' });
  }
});

// Send activity update
router.post('/activity', requireApproved, async (req, res) => {
  try {
    const currentUser = req.user;
    const { activityType, data } = req.body;

    if (!currentUser) {
      return res.status(400).json({ error: 'User not found' });
    }

    if (!activityType) {
      return res.status(400).json({ error: 'Activity type is required' });
    }

    await SocketUtils.sendActivityUpdate(
      currentUser.tenant_id,
      activityType,
      parseInt(currentUser.id),
      data,
    );

    return res.json({
      success: true,
      message: 'Activity update sent',
    });
  } catch (error) {
    Logger.error('Error sending activity update:', error);
    return res.status(500).json({ error: 'Failed to send activity update' });
  }
});

export default router;
