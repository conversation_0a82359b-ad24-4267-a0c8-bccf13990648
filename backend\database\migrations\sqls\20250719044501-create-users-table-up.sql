CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    mobile_number VARCHAR(20),
    usn VARCHAR(50) NOT NULL COMMENT 'University Seat Number for verification',
    role ENUM('STUDENT', 'ALUMNUS', 'TENANT_ADMIN', 'SUPER_ADMIN') NOT NULL,
    account_status ENUM('PENDING', 'APPROVED', 'REJECTED', 'DEACTIVATED') NOT NULL DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY idx_tenant_email (tenant_id, email),
    UNIQUE KEY idx_tenant_usn (tenant_id, usn)
);
