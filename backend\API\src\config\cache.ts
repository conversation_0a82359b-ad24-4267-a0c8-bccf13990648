import NodeCache from 'node-cache';

// Node-cache configuration optimized for performance
const getCacheConfig = (): NodeCache.Options => {
  const isProduction = process.env.NODE_ENV === 'production';

  return {
    // TTL in seconds (default 1 hour)
    stdTTL: parseInt(process.env.CACHE_DEFAULT_TTL ?? '3600', 10),

    // Check period for expired keys (every 10 minutes)
    checkperiod: parseInt(process.env.CACHE_CHECK_PERIOD ?? '600', 10),

    // Use clone to avoid reference issues
    useClones: false, // Disabled for better performance

    // Delete expired keys on get
    deleteOnExpire: true,

    // Enable statistics
    enableLegacyCallbacks: false,

    // Max keys (0 = unlimited, but we'll set a reasonable limit)
    maxKeys: parseInt(process.env.CACHE_MAX_KEYS ?? '10000', 10),

    // Production optimizations
    ...(isProduction && {
      checkperiod: 300, // More frequent cleanup in production (5 minutes)
      maxKeys: 50000, // Higher limit in production
    }),
  };
};

// Create optimized cache instances
export const cache = new NodeCache(getCacheConfig());

// Specialized caches for different use cases
export const sessionCache = new NodeCache({
  ...getCacheConfig(),
  stdTTL: parseInt(process.env.SESSION_TTL ?? '1800', 10), // 30 minutes for sessions
  maxKeys: parseInt(process.env.SESSION_MAX_KEYS ?? '5000', 10),
});

export const rateLimitCache = new NodeCache({
  ...getCacheConfig(),
  stdTTL: parseInt(process.env.RATE_LIMIT_TTL ?? '900', 10), // 15 minutes for rate limiting
  checkperiod: 60, // Check every minute for rate limits
  maxKeys: parseInt(process.env.RATE_LIMIT_MAX_KEYS ?? '10000', 10),
});

// Cache health check
export const checkCacheHealth = (): boolean => {
  try {
    // Simple health check by getting cache stats
    const stats = cache.getStats();
    return stats !== null;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Cache health check failed:', error);
    return false;
  }
};

// Optimized Cache utility functions using node-cache
export class CacheService {
  private static readonly DEFAULT_TTL = 3600; // 1 hour in seconds

  // Get cached data (synchronous for better performance)
  static get<T>(key: string): T | null {
    try {
      const cached = cache.get<T>(key);
      return cached ?? null;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  // Async version for compatibility
  static getAsync<T>(key: string): Promise<T | null> {
    return Promise.resolve(this.get<T>(key));
  }

  // Set cached data (synchronous for better performance)
  static set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): boolean {
    try {
      return cache.set(key, data, ttl);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  }

  // Async version for compatibility
  static setAsync<T>(
    key: string,
    data: T,
    ttl: number = this.DEFAULT_TTL,
  ): Promise<boolean> {
    return Promise.resolve(this.set(key, data, ttl));
  }

  // Delete cached data
  static del(key: string): boolean {
    try {
      return cache.del(key) > 0;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }

  // Async version for compatibility
  static delAsync(key: string): Promise<boolean> {
    return Promise.resolve(this.del(key));
  }

  // Delete multiple keys by pattern
  static delPattern(pattern: string): number {
    try {
      const keys = cache.keys().filter(key => key.includes(pattern));
      if (keys.length > 0) {
        return cache.del(keys);
      }
      return 0;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Cache delete pattern error for ${pattern}:`, error);
      return 0;
    }
  }

  // Async version for compatibility
  static delPatternAsync(pattern: string): Promise<number> {
    return Promise.resolve(this.delPattern(pattern));
  }

  // Check if key exists
  static exists(key: string): boolean {
    try {
      return cache.has(key);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Cache exists error for key ${key}:`, error);
      return false;
    }
  }

  // Async version for compatibility
  static existsAsync(key: string): Promise<boolean> {
    return Promise.resolve(this.exists(key));
  }

  // Increment counter
  static incr(key: string, ttl?: number): number {
    try {
      const current = cache.get<number>(key) ?? 0;
      const result = current + 1;
      cache.set(key, result, ttl ?? this.DEFAULT_TTL);
      return result;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Cache increment error for key ${key}:`, error);
      return 0;
    }
  }

  // Async version for compatibility
  static incrAsync(key: string, ttl?: number): Promise<number> {
    return Promise.resolve(this.incr(key, ttl));
  }

  // Get or set pattern (cache-aside) - optimized for performance
  static async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number = this.DEFAULT_TTL,
  ): Promise<T | null> {
    try {
      // Try to get from cache first (synchronous for speed)
      const cached = this.get<T>(key);
      if (cached !== null) {
        return cached;
      }

      // If not in cache, fetch data
      const data = await fetchFunction();
      if (data !== null && data !== undefined) {
        this.set(key, data, ttl);
      }

      return data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Cache getOrSet error for key ${key}:`, error);
      // If cache fails, still try to fetch data
      try {
        return await fetchFunction();
      } catch (fetchError) {
        // eslint-disable-next-line no-console
        console.error(`Fetch function error for key ${key}:`, fetchError);
        return null;
      }
    }
  }

  // Batch get operations (optimized for node-cache)
  static mget<T>(keys: string[]): Map<string, T | null> {
    const results = new Map<string, T | null>();

    try {
      keys.forEach(key => {
        const value = cache.get<T>(key);
        results.set(key, value ?? null);
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Cache mget error:', error);
      // Return null for all keys on error
      keys.forEach(key => results.set(key, null));
    }

    return results;
  }

  // Async version for compatibility
  static mgetAsync<T>(keys: string[]): Promise<Map<string, T | null>> {
    return Promise.resolve(this.mget<T>(keys));
  }

  // Batch set operations (optimized for node-cache)
  static mset<T>(
    entries: Array<{ key: string; data: T; ttl?: number }>,
  ): boolean {
    try {
      let allSuccess = true;
      entries.forEach(({ key, data, ttl = this.DEFAULT_TTL }) => {
        const success = cache.set(key, data, ttl);
        if (!success) {
          allSuccess = false;
        }
      });
      return allSuccess;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Cache mset error:', error);
      return false;
    }
  }

  // Async version for compatibility
  static msetAsync<T>(
    entries: Array<{ key: string; data: T; ttl?: number }>,
  ): Promise<boolean> {
    return Promise.resolve(this.mset(entries));
  }

  // Delete by pattern (optimized for node-cache)
  static deletePattern(pattern: string): number {
    try {
      const keys = cache.keys().filter(key => key.includes(pattern));
      if (keys.length === 0) {
        return 0;
      }
      return cache.del(keys);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Cache deletePattern error for pattern ${pattern}:`, error);
      return 0;
    }
  }

  // Async version for compatibility
  static deletePatternAsync(pattern: string): Promise<number> {
    return Promise.resolve(this.deletePattern(pattern));
  }

  // Clear all cache
  static clear(): boolean {
    try {
      cache.flushAll();
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Cache clear error:', error);
      return false;
    }
  }

  // Get all cache keys
  static getKeys(): string[] {
    try {
      return cache.keys();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Cache getKeys error:', error);
      return [];
    }
  }

  // Async version for compatibility
  static clearAsync(): Promise<boolean> {
    return Promise.resolve(this.clear());
  }

  // Get cache size
  static getSize(): number {
    try {
      return cache.getStats().keys;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Cache getSize error:', error);
      return 0;
    }
  }

  // Async version for compatibility
  static getSizeAsync(): Promise<number> {
    return Promise.resolve(this.getSize());
  }

  // Get cache statistics (replaces memory usage)
  static getStats(): NodeCache.Stats | null {
    try {
      return cache.getStats();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Cache getStats error:', error);
      return null;
    }
  }

  // Async version for compatibility
  static getStatsAsync(): Promise<NodeCache.Stats | null> {
    return Promise.resolve(this.getStats());
  }
}

// Cache key generators
export const CacheKeys = {
  user: (userId: string): string => `user:${userId}`,
  userProfile: (userId: string): string => `user:profile:${userId}`,
  userConnections: (userId: string): string => `user:connections:${userId}`,
  job: (jobId: string): string => `job:${jobId}`,
  jobsList: (page: number, filters: string): string =>
    `jobs:list:${page}:${filters}`,
  event: (eventId: string): string => `event:${eventId}`,
  eventsList: (page: number, filters: string): string =>
    `events:list:${page}:${filters}`,
  post: (postId: string): string => `post:${postId}`,
  postsList: (page: number, filters: string): string =>
    `posts:list:${page}:${filters}`,
  notifications: (userId: string): string => `notifications:${userId}`,
  dashboardMetrics: (): string => 'dashboard:metrics',
  userSearch: (query: string, page: number): string =>
    `search:users:${query}:${page}`,
  rateLimitUser: (userId: string): string => `ratelimit:user:${userId}`,
  rateLimitIP: (ip: string): string => `ratelimit:ip:${ip}`,
  session: (sessionId: string): string => `session:${sessionId}`,
};

// Cache event handlers for monitoring
cache.on('set', key => {
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log(`🔄 Cache SET: ${key}`);
  }
});

cache.on('del', key => {
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log(`🗑️ Cache DEL: ${key}`);
  }
});

cache.on('expired', key => {
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log(`⏰ Cache EXPIRED: ${key}`);
  }
});

cache.on('flush', () => {
  // eslint-disable-next-line no-console
  console.log('🧹 Cache flushed');
});

// Initialize cache
// eslint-disable-next-line no-console
console.log('✅ Node-cache initialized successfully');
// eslint-disable-next-line no-console
console.log(
  `📊 Cache configuration: TTL=${getCacheConfig().stdTTL}s, MaxKeys=${getCacheConfig().maxKeys}`,
);

// Graceful shutdown
const gracefulCacheShutdown = (signal: string): void => {
  // eslint-disable-next-line no-console
  console.log(`Received ${signal}, clearing caches...`);

  try {
    cache.flushAll();
    sessionCache.flushAll();
    rateLimitCache.flushAll();
    // eslint-disable-next-line no-console
    console.log('Caches cleared successfully');
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error during cache shutdown:', error);
  }
};

process.on('SIGTERM', () => gracefulCacheShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulCacheShutdown('SIGINT'));

export default cache;
