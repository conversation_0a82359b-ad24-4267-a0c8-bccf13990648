/// <reference path="../types/express.d.ts" />
import { Request, Response, NextFunction } from 'express';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../types/express';
import { Logger } from '../services/loggerService';
import {
  toAdminDashboardViewModel,
  toAdminUserActivityViewModel,
} from '../viewmodels/adminViewModel';
import { createSuccessResponse } from '../viewmodels/responseViewModel';

/**
 * Get Dashboard Statistics
 * Admin endpoint for overview statistics and metrics
 */
export const getDashboardStats = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminUser = req.user;

    if (!adminUser) {
      throw createError('User not authenticated', 401);
    }

    // Build tenant filter based on admin permissions
    const tenantFilter =
      adminUser.role === 'SUPER_ADMIN'
        ? {}
        : { tenant_id: adminUser.tenant_id };

    // Get user statistics
    const totalUsers = await prisma.user.count({
      where: tenantFilter,
    });

    const pendingUsers = await prisma.user.count({
      where: {
        ...tenantFilter,
        account_status: 'PENDING',
      },
    });

    const approvedUsers = await prisma.user.count({
      where: {
        ...tenantFilter,
        account_status: 'APPROVED',
      },
    });

    const rejectedUsers = await prisma.user.count({
      where: {
        ...tenantFilter,
        account_status: 'REJECTED',
      },
    });

    const deactivatedUsers = await prisma.user.count({
      where: {
        ...tenantFilter,
        account_status: 'DEACTIVATED',
      },
    });

    const totalAlumni = await prisma.user.count({
      where: {
        ...tenantFilter,
        role: 'ALUMNUS',
        account_status: 'APPROVED',
      },
    });

    const totalStudents = await prisma.user.count({
      where: {
        ...tenantFilter,
        role: 'STUDENT',
        account_status: 'APPROVED',
      },
    });

    // Get content statistics
    const totalPosts = await prisma.generalPost.count({
      where: tenantFilter,
    });

    const totalJobs = await prisma.job.count({
      where: tenantFilter,
    });

    const totalEvents = await prisma.event.count({
      where: tenantFilter,
    });

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentUsers = await prisma.user.count({
      where: {
        ...tenantFilter,
        created_at: {
          gte: thirtyDaysAgo,
        },
      },
    });

    const recentPosts = await prisma.generalPost.count({
      where: {
        ...tenantFilter,
        created_at: {
          gte: thirtyDaysAgo,
        },
      },
    });

    const recentJobs = await prisma.job.count({
      where: {
        ...tenantFilter,
        created_at: {
          gte: thirtyDaysAgo,
        },
      },
    });

    const recentEvents = await prisma.event.count({
      where: {
        ...tenantFilter,
        created_at: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Get tenant statistics (for super admin)
    let tenantStats = undefined;
    if (adminUser.role === 'SUPER_ADMIN') {
      tenantStats = await prisma.tenant.findMany({
        where: {
          is_active: true,
        },
        include: {
          _count: {
            select: {
              users: true,
              general_posts: true,
              jobs: true,
              events: true,
            },
          },
        },
        orderBy: {
          name: 'asc',
        },
      });
    }

    const dashboardData = toAdminDashboardViewModel({
      totalUsers,
      pendingUsers,
      approvedUsers,
      rejectedUsers,
      deactivatedUsers,
      totalAlumni,
      totalStudents,
      totalPosts,
      totalJobs,
      totalEvents,
      recentUsers,
      recentPosts,
      recentJobs,
      recentEvents,
      tenantStats,
    });

    const response = createSuccessResponse(dashboardData);
    res.json(response);

    // Log admin action
    Logger.info('Admin accessed dashboard', {
      adminId: adminUser.id,
      adminEmail: adminUser.email,
      adminRole: adminUser.role,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get User Activity Report
 * Admin endpoint for detailed user activity analytics
 */
export const getUserActivityReport = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminUser = req.user;

    if (!adminUser) {
      throw createError('User not authenticated', 401);
    }

    const startDate = req.query.start_date as string;
    const endDate = req.query.end_date as string;

    // Default to last 30 days if no dates provided
    const start = startDate
      ? new Date(startDate)
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    // Build tenant filter
    const tenantFilter =
      adminUser.role === 'SUPER_ADMIN'
        ? {}
        : { tenant_id: adminUser.tenant_id };

    // Get daily registration counts
    const registrations = (await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM User 
      WHERE created_at >= ${start} 
        AND created_at <= ${end}
        ${adminUser.role !== 'SUPER_ADMIN' ? `AND tenant_id = ${adminUser.tenant_id}` : ''}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `) as Array<{ date: Date; count: bigint }>;

    // Get status breakdown
    const statusBreakdown = await prisma.user.groupBy({
      by: ['account_status'],
      where: {
        ...tenantFilter,
        created_at: {
          gte: start,
          lte: end,
        },
      },
      _count: {
        id: true,
      },
    });

    // Get role breakdown
    const roleBreakdown = await prisma.user.groupBy({
      by: ['role'],
      where: {
        ...tenantFilter,
        created_at: {
          gte: start,
          lte: end,
        },
      },
      _count: {
        id: true,
      },
    });

    // Get most active users
    const activeUsers = await prisma.user.findMany({
      where: {
        ...tenantFilter,
        account_status: 'APPROVED',
      },
      include: {
        _count: {
          select: {
            general_posts: {
              where: {
                created_at: {
                  gte: start,
                  lte: end,
                },
              },
            },
            jobs: {
              where: {
                created_at: {
                  gte: start,
                  lte: end,
                },
              },
            },
            events: {
              where: {
                created_at: {
                  gte: start,
                  lte: end,
                },
              },
            },
          },
        },
      },
      orderBy: [
        {
          general_posts: {
            _count: 'desc',
          },
        },
      ],
      take: 10,
    });

    const reportData = toAdminUserActivityViewModel({
      dateRange: {
        start: start.toISOString(),
        end: end.toISOString(),
      },
      registrations: registrations.map(r => ({
        date: r.date.toISOString().split('T')[0],
        count: Number(r.count),
      })),
      statusBreakdown,
      roleBreakdown,
      activeUsers: activeUsers.map(user => ({
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        role: user.role,
        _count: {
          general_posts: user._count.general_posts,
          jobs: user._count.jobs,
          events: user._count.events,
        },
      })),
    });

    const response = createSuccessResponse(reportData);
    res.json(response);

    // Log admin action
    Logger.info('Admin generated user activity report', {
      adminId: adminUser.id,
      adminEmail: adminUser.email,
      dateRange: { start: start.toISOString(), end: end.toISOString() },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get Content Activity Report
 * Admin endpoint for content creation and engagement analytics
 */
export const getContentActivityReport = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminUser = req.user;

    if (!adminUser) {
      throw createError('User not authenticated', 401);
    }

    const startDate = req.query.start_date as string;
    const endDate = req.query.end_date as string;

    // Default to last 30 days if no dates provided
    const start = startDate
      ? new Date(startDate)
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    // Build tenant filter
    const tenantFilter =
      adminUser.role === 'SUPER_ADMIN'
        ? {}
        : { tenant_id: adminUser.tenant_id };

    // Get daily content creation counts
    const dailyPosts = (await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM GeneralPost 
      WHERE created_at >= ${start} 
        AND created_at <= ${end}
        ${adminUser.role !== 'SUPER_ADMIN' ? `AND tenant_id = ${adminUser.tenant_id}` : ''}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `) as Array<{ date: Date; count: bigint }>;

    const dailyJobs = (await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM Job 
      WHERE created_at >= ${start} 
        AND created_at <= ${end}
        ${adminUser.role !== 'SUPER_ADMIN' ? `AND tenant_id = ${adminUser.tenant_id}` : ''}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `) as Array<{ date: Date; count: bigint }>;

    const dailyEvents = (await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM Event 
      WHERE created_at >= ${start} 
        AND created_at <= ${end}
        ${adminUser.role !== 'SUPER_ADMIN' ? `AND tenant_id = ${adminUser.tenant_id}` : ''}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `) as Array<{ date: Date; count: bigint }>;

    // Get top content creators
    const topPostCreators = await prisma.user.findMany({
      where: {
        ...tenantFilter,
        account_status: 'APPROVED',
      },
      include: {
        _count: {
          select: {
            general_posts: {
              where: {
                created_at: {
                  gte: start,
                  lte: end,
                },
              },
            },
          },
        },
      },
      orderBy: {
        general_posts: {
          _count: 'desc',
        },
      },
      take: 10,
    });

    const reportData = {
      date_range: {
        start: start.toISOString(),
        end: end.toISOString(),
      },
      daily_activity: {
        posts: dailyPosts.map(p => ({
          date: p.date.toISOString().split('T')[0],
          count: Number(p.count),
        })),
        jobs: dailyJobs.map(j => ({
          date: j.date.toISOString().split('T')[0],
          count: Number(j.count),
        })),
        events: dailyEvents.map(e => ({
          date: e.date.toISOString().split('T')[0],
          count: Number(e.count),
        })),
      },
      top_creators: topPostCreators.map(user => ({
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        role: user.role,
        posts_count: user._count.general_posts,
      })),
    };

    const response = createSuccessResponse(reportData);
    res.json(response);

    // Log admin action
    Logger.info('Admin generated content activity report', {
      adminId: adminUser.id,
      adminEmail: adminUser.email,
      dateRange: { start: start.toISOString(), end: end.toISOString() },
    });
  } catch (error) {
    next(error);
  }
};
