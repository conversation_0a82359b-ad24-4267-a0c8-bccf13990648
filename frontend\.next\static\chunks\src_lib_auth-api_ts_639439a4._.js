(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/auth-api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthApiError": (()=>AuthApiError),
    "authApiService": (()=>authApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
// Create a separate axios instance for auth API calls
const authApi = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: "http://localhost:5000/api/auth",
    timeout: 10000,
    headers: {
        "Content-Type": "application/json"
    }
});
class AuthApiError extends Error {
    statusCode;
    originalError;
    constructor(message, statusCode, originalError){
        super(message), this.statusCode = statusCode, this.originalError = originalError;
        this.name = "AuthApiError";
    }
}
const authApiService = {
    /**
   * Login user with email and password
   */ async login (email, password) {
        try {
            const response = await authApi.post("/login", {
                email,
                password,
                tenant_id: 20
            });
            return response.data;
        } catch (error) {
            const message = error.response?.data?.error || error.response?.data?.message || "Login failed. Please try again.";
            throw new AuthApiError(message, error.response?.status, error);
        }
    },
    /**
   * Register new user
   */ async register (userData) {
        try {
            const role = userData.userType === "student" ? "STUDENT" : "ALUMNUS";
            const payload = {
                tenant_id: 20,
                full_name: userData.fullName,
                email: userData.email,
                password: userData.password,
                mobile_number: userData.mobileNumber,
                course_name: userData.course,
                batch_year: parseInt(userData.batch),
                usn: userData.usn,
                role: role
            };
            const response = await authApi.post("/register", payload);
            return response.data;
        } catch (error) {
            const message = error.response?.data?.error || error.response?.data?.message || "Registration failed. Please try again.";
            throw new AuthApiError(message, error.response?.status, error);
        }
    },
    /**
   * Logout user and invalidate token
   */ async logout (accessToken) {
        try {
            const headers = {};
            if (accessToken) {
                headers.Authorization = `Bearer ${accessToken}`;
            }
            const response = await authApi.post("/logout", {}, {
                headers
            });
            return response.data;
        } catch (error) {
            const message = error.response?.data?.error || error.response?.data?.message || "Logout failed. Please try again.";
            throw new AuthApiError(message, error.response?.status, error);
        }
    },
    /**
   * Refresh access token
   */ async refreshToken (refreshToken) {
        try {
            const response = await authApi.post("/refresh", {
                refreshToken
            });
            return response.data;
        } catch (error) {
            const message = error.response?.data?.error || error.response?.data?.message || "Token refresh failed. Please login again.";
            throw new AuthApiError(message, error.response?.status, error);
        }
    },
    /**
   * Verify if current token is valid
   */ async verifyToken (accessToken) {
        try {
            const response = await authApi.get("/verify", {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return {
                valid: true,
                user: response.data.user
            };
        } catch (error) {
            return {
                valid: false
            };
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_lib_auth-api_ts_639439a4._.js.map