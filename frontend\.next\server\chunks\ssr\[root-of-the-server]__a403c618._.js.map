{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IonAlumni-Frontend-setup/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IonAlumni-Frontend-setup/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IonAlumni-Frontend-setup/frontend/src/lib/mock-data.ts"], "sourcesContent": ["export type User = {\n  id: number;\n  role: 'alumnus' | 'student';\n  name: string;\n  email: string;\n  avatar: string;\n  course: string;\n  batch: number;\n  usn?: string;\n  location?: string;\n  company?: string;\n  jobTitle?: string;\n  linkedin?: string;\n  followedAlumni?: number[];\n};\n\nexport type Job = {\n  id: number;\n  authorId: number;\n  title: string;\n  company: string;\n  location: string;\n  description: string;\n  type: 'Full-time' | 'Internship' | 'Part-time';\n  experienceLevel: string;\n  postedAt: string;\n};\n\nexport type Event = {\n  id: number;\n  authorId: number;\n  title: string;\n  date: string;\n  location: string;\n  description: string;\n  image: string;\n  rsvpLink: string;\n};\n\nexport type Post = {\n  id: number;\n  authorId: number;\n  title?: string;\n  content: string;\n  postedAt: string;\n};\n\nexport const users: User[] = [\n  {\n    id: 1,\n    role: 'alumnus',\n    name: '<PERSON><PERSON>',\n    email: '<EMAIL>',\n    avatar: 'https://placehold.co/100x100/EAD3F8/9434A3',\n    course: 'Computer Science',\n    batch: 2015,\n    location: 'Bengaluru',\n    company: 'Innovate Corp',\n    jobTitle: 'Senior Software Engineer',\n    linkedin: 'https://linkedin.com/in/priyasharma',\n  },\n  {\n    id: 2,\n    role: 'alumnus',\n    name: '<PERSON><PERSON>',\n    email: '<EMAIL>',\n    avatar: 'https://placehold.co/100x100/D3E4F8/3476A3',\n    course: 'Mechanical Engineering',\n    batch: 2012,\n    location: 'Mumbai',\n    company: 'BuildIt Solutions',\n    jobTitle: 'Project Manager',\n    linkedin: 'https://linkedin.com/in/rajeshkumar',\n  },\n  {\n    id: 3,\n    role: 'student',\n    name: 'Anjali Singh',\n    email: '<EMAIL>',\n    avatar: 'https://placehold.co/100x100/F8D3D3/A33434',\n    course: 'Computer Science',\n    batch: 2025,\n    usn: '*********',\n    followedAlumni: [1, 2],\n  },\n  {\n    id: 4,\n    role: 'alumnus',\n    name: 'Vikram Mehta',\n    email: '<EMAIL>',\n    avatar: 'https://placehold.co/100x100/D3F8D3/34A334',\n    course: 'Electronics & Communication',\n    batch: 2018,\n    location: 'Pune',\n    company: 'Circuitry Inc.',\n    jobTitle: 'Hardware Engineer',\n    linkedin: 'https://linkedin.com/in/vikrammehta',\n  },\n];\n\nexport const jobs: Job[] = [\n  {\n    id: 1,\n    authorId: 1,\n    title: 'Frontend Developer (React)',\n    company: 'Innovate Corp',\n    location: 'Remote',\n    description: 'Looking for a skilled React developer to join our dynamic team. Experience with Next.js and TypeScript is a plus.',\n    type: 'Full-time',\n    experienceLevel: 'Mid-Level',\n    postedAt: '2 days ago',\n  },\n  {\n    id: 2,\n    authorId: 2,\n    title: 'Mechanical Design Intern',\n    company: 'BuildIt Solutions',\n    location: 'Mumbai',\n    description: 'Exciting internship opportunity for final year Mechanical Engineering students to work on real-world projects.',\n    type: 'Internship',\n    experienceLevel: 'Internship',\n    postedAt: '1 week ago',\n  },\n];\n\nexport const events: Event[] = [\n  {\n    id: 1,\n    authorId: 1,\n    title: 'Alumni Networking Meetup',\n    date: '2024-08-15T18:00:00Z',\n    location: 'Online',\n    description: 'A virtual meetup for all alumni to connect, network, and share experiences. Special focus on career growth in tech.',\n    image: 'https://placehold.co/600x400',\n    rsvpLink: '#',\n  },\n  {\n    id: 2,\n    authorId: 4,\n    title: 'Workshop on Embedded Systems',\n    date: '2024-08-20T10:00:00Z',\n    location: 'College Auditorium',\n    description: 'A hands-on workshop covering the fundamentals of embedded systems and IoT. Open to all students.',\n    image: 'https://placehold.co/600x400',\n    rsvpLink: '#',\n  },\n];\n\nexport const posts: Post[] = [\n  {\n    id: 1,\n    authorId: 1,\n    content: \"Just hit a major milestone in our project! It's incredible to see how the principles we learned in our Data Structures class still apply every day. To all the students, never underestimate the fundamentals!\",\n    postedAt: '4 hours ago',\n  },\n  {\n    id: 2,\n    authorId: 2,\n    title: 'Advice for Aspiring Project Managers',\n    content: \"The key to successful project management isn't just about tools and charts; it's about communication. Learn to listen to your team and stakeholders. That's the real secret sauce.\",\n    postedAt: '3 days ago',\n  },\n];\n\nexport const getAuthor = (authorId: number) => users.find((user) => user.id === authorId);\n\nexport const currentUser: User = users[2]; // Simulating logged-in user as Anjali Singh (student)\n// export const currentUser: User = users[0]; // To test as an Alumnus\n"], "names": [], "mappings": ";;;;;;;;AA+CO,MAAM,QAAgB;IAC3B;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,UAAU;QACV,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,UAAU;QACV,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,KAAK;QACL,gBAAgB;YAAC;YAAG;SAAE;IACxB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,UAAU;QACV,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAEM,MAAM,OAAc;IACzB;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,SAAS;QACT,UAAU;QACV,aAAa;QACb,MAAM;QACN,iBAAiB;QACjB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,SAAS;QACT,UAAU;QACV,aAAa;QACb,MAAM;QACN,iBAAiB;QACjB,UAAU;IACZ;CACD;AAEM,MAAM,SAAkB;IAC7B;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,UAAU;IACZ;CACD;AAEM,MAAM,QAAgB;IAC3B;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,SAAS;QACT,UAAU;IACZ;CACD;AAEM,MAAM,YAAY,CAAC,WAAqB,MAAM,IAAI,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;AAEzE,MAAM,cAAoB,KAAK,CAAC,EAAE,EAAE,sDAAsD;CACjG,sEAAsE", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IonAlumni-Frontend-setup/frontend/src/components/public-footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\n\nexport function PublicFooter() {\n    return (\n        <footer className=\"flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center px-4 md:px-6 border-t bg-muted\">\n            <p className=\"text-xs text-muted-foreground\">&copy; 2024 Ion-Alumni. All rights reserved.</p>\n            <nav className=\"sm:ml-auto flex gap-4 sm:gap-6\">\n                <Link href=\"#\" className=\"text-xs hover:underline underline-offset-4\" prefetch={false}>\n                    Terms of Service\n                </Link>\n                <Link href=\"#\" className=\"text-xs hover:underline underline-offset-4\" prefetch={false}>\n                    Privacy\n                </Link>\n            </nav>\n        </footer>\n    )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACZ,qBACI,8OAAC;QAAO,WAAU;;0BACd,8OAAC;gBAAE,WAAU;0BAAgC;;;;;;0BAC7C,8OAAC;gBAAI,WAAU;;kCACX,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;wBAA6C,UAAU;kCAAO;;;;;;kCAGvF,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;wBAA6C,UAAU;kCAAO;;;;;;;;;;;;;;;;;;AAMvG", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IonAlumni-Frontend-setup/frontend/src/components/public-header.tsx"], "sourcesContent": ["'use client';\nimport Link from \"next/link\";\nimport { But<PERSON> } from \"./ui/button\";\n\nexport function PublicHeader() {\n  return (\n    <header className=\"px-4 lg:px-6 h-14 flex items-center bg-background border-b sticky top-0 z-50\">\n      <Link href=\"/\" className=\"flex items-center justify-center\">\n        <span className=\"font-headline text-xl font-bold text-primary\">Ion-Alumni</span>\n      </Link>\n      <nav className=\"ml-auto flex gap-4 sm:gap-6\">\n        <Button variant=\"ghost\" asChild>\n            <Link href=\"/login\">Login</Link>\n        </Button>\n        <Button asChild className=\"bg-accent hover:bg-accent/90\">\n            <Link href=\"/signup\">Sign Up</Link>\n        </Button>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;gBAAI,WAAU;0BACvB,cAAA,8OAAC;oBAAK,WAAU;8BAA+C;;;;;;;;;;;0BAEjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,OAAO;kCAC3B,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAS;;;;;;;;;;;kCAExB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,WAAU;kCACtB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAKjC", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IonAlumni-Frontend-setup/frontend/app/page.tsx"], "sourcesContent": ["'use client';\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { events, jobs, users } from \"@/lib/mock-data\";\nimport { ArrowRight, Briefcase, Calendar, MapPin, Users } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { PublicFooter } from \"@/components/public-footer\";\nimport { PublicHeader } from \"@/components/public-header\";\n\nconst JobCard = ({ job }: { job: (typeof jobs)[0] }) => {\n  return (\n    <Card className=\"flex flex-col\">\n      <CardHeader>\n        <div className=\"flex items-start gap-4\">\n            <div className=\"flex h-12 w-12 items-center justify-center rounded-lg bg-muted\">\n                <Briefcase className=\"h-6 w-6 text-primary\" />\n            </div>\n            <div>\n                <CardTitle className=\"font-headline text-lg\">{job.title}</CardTitle>\n                <CardDescription>{job.company}</CardDescription>\n            </div>\n        </div>\n      </CardHeader>\n      <CardContent className=\"flex-grow\">\n        <div className=\"flex w-full items-center justify-between text-sm text-muted-foreground\">\n          <div className=\"flex items-center gap-1.5\">\n            <MapPin className=\"h-4 w-4\" /> {job.location}\n          </div>\n          <div className=\"rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground\">{job.type}</div>\n        </div>\n      </CardContent>\n      <CardFooter>\n        <Button asChild className=\"w-full\">\n            <Link href=\"/login\">View Details <ArrowRight className=\"ml-2 h-4 w-4\" /></Link>\n        </Button>\n      </CardFooter>\n    </Card>\n  )\n}\n\nconst EventCard = ({ event }: { event: (typeof events)[0] }) => {\n  return (\n    <Card className=\"overflow-hidden\">\n      <CardHeader className=\"p-0\">\n        <img src={event.image} alt={event.title} data-ai-hint=\"event poster\" className=\"aspect-video w-full object-cover\" />\n      </CardHeader>\n      <CardContent className=\"p-4\">\n        <p className=\"text-sm font-semibold text-accent\">{new Date(event.date).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}</p>\n        <h3 className=\"font-headline text-lg font-bold\">{event.title}</h3>\n        <p className=\"text-sm text-muted-foreground line-clamp-2\">{event.description}</p>\n      </CardContent>\n      <CardFooter className=\"flex justify-between p-4 pt-0\">\n        <div className=\"flex items-center text-sm text-muted-foreground\">\n            <MapPin className=\"mr-1.5 h-4 w-4\" /> {event.location}\n        </div>\n        <Button asChild variant=\"outline\" size=\"sm\">\n            <Link href=\"/login\">Details & RSVP</Link>\n        </Button>\n      </CardFooter>\n    </Card>\n  )\n}\n\n\nexport default function LandingPage() {\n    const featuredJobs = jobs.slice(0, 3);\n    const upcomingEvents = events.slice(0, 3);\n    const alumniCount = users.filter(u => u.role === 'alumnus').length;\n\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <PublicHeader />\n      <main className=\"flex-1\">\n        {/* Hero Section */}\n        <section className=\"w-full py-12 md:py-24 lg:py-32 bg-background\">\n          <div className=\"container px-4 md:px-6\">\n            <div className=\"grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]\">\n              <div className=\"flex flex-col justify-center space-y-4\">\n                <div className=\"space-y-2\">\n                  <h1 className=\"font-headline text-4xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none text-primary\">\n                    Connect. Grow. Succeed.\n                  </h1>\n                  <p className=\"max-w-[600px] text-muted-foreground md:text-xl\">\n                    Welcome to Ion-Alumni, the exclusive portal for our college community. Reconnect with peers, discover opportunities, and give back to your alma mater.\n                  </p>\n                </div>\n                <div className=\"flex flex-col gap-2 min-[400px]:flex-row\">\n                  <Button asChild size=\"lg\" className=\"bg-accent hover:bg-accent/90\">\n                    <Link href=\"/signup\">Join Now</Link>\n                  </Button>\n                  <Button asChild size=\"lg\" variant=\"secondary\">\n                    <Link href=\"/login\">Member Login</Link>\n                  </Button>\n                </div>\n              </div>\n              <img\n                src=\"https://images.jdmagicbox.com/v2/comp/bangalore/a1/080pxx80.xx80.180921194648.y3a1/catalogue/ionidea-whitefield-bangalore-software-companies-qgohvetji9.jpg\"\n                width=\"600\"\n                height=\"400\"\n                alt=\"Alumni collaborating\"\n                data-ai-hint=\"alumni collaborating\"\n                className=\"mx-auto aspect-video overflow-hidden rounded-xl object-cover sm:w-full lg:order-last\"\n              />\n            </div>\n          </div>\n        </section>\n\n      {/* Video Introduction Section */}\n<section className=\"w-full py-12 md:py-24 lg:py-32 bg-muted\">\n  <div className=\"container px-4 md:px-6\">\n    <div className=\"flex flex-col items-center text-center space-y-6\">\n      <h2 className=\"font-headline text-3xl sm:text-5xl font-bold text-primary\">\n        Watch: What is Ion-Alumni?\n      </h2>\n      <p className=\"max-w-2xl text-muted-foreground md:text-xl\">\n        Discover how Ion-Alumni connects graduates, shares opportunities, and builds a stronger college community.\n      </p>\n\n      {/* Video Embed or HTML5 video */}\n      <div className=\"w-full max-w-4xl aspect-video rounded-xl overflow-hidden shadow-lg\">\n        <video controls poster=\"https://placehold.co/800x450.png?text=Ion-Alumni+Preview\" className=\"w-full h-full object-cover rounded-lg\">\n          <source src=\"/video/ion-alumni-intro.mp4\" type=\"video/mp4\" />\n          Your browser does not support the video tag.\n        </video>\n      </div>\n    </div>\n  </div>\n</section>\n\n\n        {/* Stats Section */}\n        <section className=\"w-full py-12 md:py-24 lg:py-32 bg-muted\">\n            <div className=\"container px-4 md:px-6\">\n                <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-6\">\n                    <div className=\"text-center\">\n                        <Users className=\"mx-auto h-12 w-12 text-primary\" />\n                        <h3 className=\"mt-2 text-3xl font-bold\">{alumniCount}+</h3>\n                        <p className=\"text-muted-foreground\">Active Alumni</p>\n                    </div>\n                     <div className=\"text-center\">\n                        <Briefcase className=\"mx-auto h-12 w-12 text-primary\" />\n                        <h3 className=\"mt-2 text-3xl font-bold\">{jobs.length}+</h3>\n                        <p className=\"text-muted-foreground\">Jobs Posted</p>\n                    </div>\n                     <div className=\"text-center\">\n                        <Calendar className=\"mx-auto h-12 w-12 text-primary\" />\n                        <h3 className=\"mt-2 text-3xl font-bold\">{events.length}+</h3>\n                        <p className=\"text-muted-foreground\">Events Organized</p>\n                    </div>\n                     <div className=\"text-center\">\n                        <Users className=\"mx-auto h-12 w-12 text-primary\" />\n                        <h3 className=\"mt-2 text-3xl font-bold\">100+</h3>\n                        <p className=\"text-muted-foreground\">Companies</p>\n                    </div>\n                </div>\n            </div>\n        </section>\n\n        {/* Featured Jobs Section */}\n        <section className=\"w-full py-12 md:py-24 lg:py-32 bg-background\">\n          <div className=\"container px-4 md:px-6\">\n            <div className=\"flex flex-col items-center justify-center space-y-4 text-center\">\n              <div className=\"space-y-2\">\n                <h2 className=\"font-headline text-3xl font-bold tracking-tighter sm:text-5xl\">Featured Job Opportunities</h2>\n                <p className=\"max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed\">\n                  Explore exclusive job and internship openings by our esteemed alumni network.\n                </p>\n              </div>\n            </div>\n            <div className=\"mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 pt-12\">\n                {featuredJobs.map(job => <JobCard key={job.id} job={job} />)}\n            </div>\n          </div>\n        </section>\n\n        {/* Upcoming Events Section */}\n        <section className=\"w-full py-12 md:py-24 lg:py-32 bg-muted\">\n          <div className=\"container px-4 md:px-6\">\n             <div className=\"flex flex-col items-center justify-center space-y-4 text-center\">\n              <div className=\"space-y-2\">\n                <h2 className=\"font-headline text-3xl font-bold tracking-tighter sm:text-5xl\">Upcoming Events</h2>\n                <p className=\"max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed\">\n                  Join our upcoming events to network, learn, and reconnect with the community.\n                </p>\n              </div>\n            </div>\n            <div className=\"mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 pt-12\">\n                {upcomingEvents.map(event => <EventCard key={event.id} event={event} />)}\n            </div>\n          </div>\n        </section>\n      </main>\n      <PublicFooter />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;;AASA,MAAM,UAAU,CAAC,EAAE,GAAG,EAA6B;IACjD,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC,4MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEzB,8OAAC;;8CACG,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAyB,IAAI,KAAK;;;;;;8CACvD,8OAAC,gIAAA,CAAA,kBAAe;8CAAE,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;;0BAIvC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCAAE,IAAI,QAAQ;;;;;;;sCAE9C,8OAAC;4BAAI,WAAU;sCAAmF,IAAI,IAAI;;;;;;;;;;;;;;;;;0BAG9G,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;oBAAC,WAAU;8BACtB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;;4BAAS;0CAAa,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKnE;AAEA,MAAM,YAAY,CAAC,EAAE,KAAK,EAAiC;IACzD,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,KAAK,MAAM,KAAK;oBAAE,KAAK,MAAM,KAAK;oBAAE,gBAAa;oBAAe,WAAU;;;;;;;;;;;0BAEjF,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAE,WAAU;kCAAqC,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB,CAAC,SAAS;4BAAE,SAAS;4BAAQ,OAAO;4BAAQ,KAAK;wBAAU;;;;;;kCACpJ,8OAAC;wBAAG,WAAU;kCAAmC,MAAM,KAAK;;;;;;kCAC5D,8OAAC;wBAAE,WAAU;kCAA8C,MAAM,WAAW;;;;;;;;;;;;0BAE9E,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAmB;4BAAE,MAAM,QAAQ;;;;;;;kCAEzD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,SAAQ;wBAAU,MAAK;kCACnC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAS;;;;;;;;;;;;;;;;;;;;;;;AAKhC;AAGe,SAAS;IACpB,MAAM,eAAe,0HAAA,CAAA,OAAI,CAAC,KAAK,CAAC,GAAG;IACnC,MAAM,iBAAiB,0HAAA,CAAA,SAAM,CAAC,KAAK,CAAC,GAAG;IACvC,MAAM,cAAc,0HAAA,CAAA,QAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;IAEpE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,eAAY;;;;;0BACb,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA8F;;;;;;kEAG5G,8OAAC;wDAAE,WAAU;kEAAiD;;;;;;;;;;;;0DAIhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,MAAK;wDAAK,WAAU;kEAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAU;;;;;;;;;;;kEAEvB,8OAAC,kIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,MAAK;wDAAK,SAAQ;kEAChC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAS;;;;;;;;;;;;;;;;;;;;;;;kDAI1B,8OAAC;wCACC,KAAI;wCACJ,OAAM;wCACN,QAAO;wCACP,KAAI;wCACJ,gBAAa;wCACb,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAO1B,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAK1D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,QAAQ;4CAAC,QAAO;4CAA2D,WAAU;;8DAC1F,8OAAC;oDAAO,KAAI;oDAA8B,MAAK;;;;;;gDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/D,8OAAC;wBAAQ,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAG,WAAU;;oDAA2B;oDAAY;;;;;;;0DACrD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;;0DACZ,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAG,WAAU;;oDAA2B,0HAAA,CAAA,OAAI,CAAC,MAAM;oDAAC;;;;;;;0DACrD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;;0DACZ,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAG,WAAU;;oDAA2B,0HAAA,CAAA,SAAM,CAAC,MAAM;oDAAC;;;;;;;0DACvD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;;0DACZ,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrD,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgE;;;;;;0DAC9E,8OAAC;gDAAE,WAAU;0DAAiG;;;;;;;;;;;;;;;;;8CAKlH,8OAAC;oCAAI,WAAU;8CACV,aAAa,GAAG,CAAC,CAAA,oBAAO,8OAAC;4CAAqB,KAAK;2CAAb,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;kCAMrD,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACZ,8OAAC;oCAAI,WAAU;8CACd,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgE;;;;;;0DAC9E,8OAAC;gDAAE,WAAU;0DAAiG;;;;;;;;;;;;;;;;;8CAKlH,8OAAC;oCAAI,WAAU;8CACV,eAAe,GAAG,CAAC,CAAA,sBAAS,8OAAC;4CAAyB,OAAO;2CAAjB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK/D,8OAAC,sIAAA,CAAA,eAAY;;;;;;;;;;;AAGnB", "debugId": null}}]}