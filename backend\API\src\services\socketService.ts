import { Server as SocketIOServer } from 'socket.io';
import { PrismaClient } from '@prisma/client';
import { getSocketIO, SOCKET_EVENTS, SOCKET_ROOMS } from '../config/socket';
import {
  AuthenticatedSocket,
  OnlineUser,
  UserStatus,
  ChatMessage,
  SocketNotification,
  FollowRequest,
  EventUpdate,
  JobUpdate,
  PostUpdate,
  TypingIndicator,
} from '../types/socket';
import { Logger } from './loggerService';

const prisma = new PrismaClient();

class SocketService {
  private io: SocketIOServer | null = null;
  private onlineUsers = new Map<number, OnlineUser>();
  private userSockets = new Map<number, Set<string>>();
  private typingUsers = new Map<string, Set<number>>();

  // Initialize the service with Socket.IO instance
  initialize(io: SocketIOServer): void {
    this.io = io;
    Logger.info('SocketService initialized');
  }

  // Get Socket.IO instance
  private getIO(): SocketIOServer {
    if (!this.io) {
      this.io = getSocketIO();
    }
    return this.io;
  }

  // User connection management
  async handleUserConnection(socket: AuthenticatedSocket): Promise<void> {
    if (!socket.userId || !socket.tenantId) {
      return;
    }

    try {
      // Fetch user details
      const user = await prisma.user.findUnique({
        where: { id: socket.userId },
        select: {
          id: true,
          full_name: true,
          tenant_id: true,
        },
      });

      if (!user) {
        return;
      }

      // Add user to online users
      const onlineUser: OnlineUser = {
        userId: user.id,
        tenantId: user.tenant_id,
        fullName: user.full_name,
        status: UserStatus.ONLINE,
        lastSeen: new Date(),
        socketId: socket.id,
      };

      this.onlineUsers.set(user.id, onlineUser);

      // Track user sockets
      if (!this.userSockets.has(user.id)) {
        this.userSockets.set(user.id, new Set());
      }
      this.userSockets.get(user.id)!.add(socket.id);

      // Join user-specific rooms
      await socket.join([
        SOCKET_ROOMS.USER(user.id),
        SOCKET_ROOMS.TENANT(user.tenant_id),
        SOCKET_ROOMS.NOTIFICATIONS(user.id),
        SOCKET_ROOMS.ONLINE_USERS(user.tenant_id),
      ]);

      // Notify tenant about user coming online
      socket
        .to(SOCKET_ROOMS.TENANT(user.tenant_id))
        .emit(SOCKET_EVENTS.USER_ONLINE, {
          userId: user.id,
          fullName: user.full_name,
          status: UserStatus.ONLINE,
          timestamp: new Date(),
        });

      // Send online users list to the connected user
      const tenantOnlineUsers = Array.from(this.onlineUsers.values()).filter(
        u => u.tenantId === user.tenant_id,
      );

      socket.emit(SOCKET_EVENTS.ONLINE_USERS_LIST, tenantOnlineUsers);

      Logger.info(
        `User ${user.id} (${user.full_name}) connected via socket ${socket.id}`,
      );
    } catch (error) {
      Logger.error('Error handling user connection:', error);
    }
  }

  // User disconnection management
  async handleUserDisconnection(socket: AuthenticatedSocket) {
    if (!socket.userId || !socket.tenantId) {
      return;
    }

    try {
      const { userId } = socket;
      const { tenantId } = socket;

      // Remove socket from user's socket set
      const userSocketSet = this.userSockets.get(userId);
      if (userSocketSet) {
        userSocketSet.delete(socket.id);

        // If no more sockets for this user, mark as offline
        if (userSocketSet.size === 0) {
          this.userSockets.delete(userId);

          const onlineUser = this.onlineUsers.get(userId);
          if (onlineUser) {
            onlineUser.status = UserStatus.OFFLINE;
            onlineUser.lastSeen = new Date();

            // Notify tenant about user going offline
            socket
              .to(SOCKET_ROOMS.TENANT(tenantId))
              .emit(SOCKET_EVENTS.USER_OFFLINE, {
                userId,
                status: UserStatus.OFFLINE,
                lastSeen: onlineUser.lastSeen,
                timestamp: new Date(),
              });

            // Remove from online users after a delay (in case of reconnection)
            setTimeout(() => {
              const currentSocketSet = this.userSockets.get(userId);
              if (!currentSocketSet || currentSocketSet.size === 0) {
                this.onlineUsers.delete(userId);
              }
            }, 30000); // 30 seconds delay
          }
        }
      }

      Logger.info(`User ${userId} disconnected from socket ${socket.id}`);
    } catch (error) {
      Logger.error('Error handling user disconnection:', error);
    }
  }

  // Send notification to specific user
  async sendNotificationToUser(
    userId: number,
    notification: SocketNotification,
  ) {
    try {
      const io = this.getIO();

      // Send to user's notification room
      io.to(SOCKET_ROOMS.NOTIFICATIONS(userId)).emit(
        SOCKET_EVENTS.RECEIVE_NOTIFICATION,
        notification,
      );

      // Update notification count
      const unreadCount = await this.getUnreadNotificationCount(userId);
      io.to(SOCKET_ROOMS.USER(userId)).emit(SOCKET_EVENTS.NOTIFICATION_COUNT, {
        count: unreadCount,
      });

      Logger.info(`Notification sent to user ${userId}:`, notification.type);
    } catch (error) {
      Logger.error('Error sending notification to user:', error);
    }
  }

  // Send notification to multiple users
  async sendNotificationToUsers(
    userIds: number[],
    notification: SocketNotification,
  ) {
    const promises = userIds.map(userId =>
      this.sendNotificationToUser(userId, { ...notification, userId }),
    );
    await Promise.all(promises);
  }

  // Send notification to entire tenant
  async sendNotificationToTenant(
    tenantId: number,
    notification: SocketNotification,
  ) {
    try {
      const io = this.getIO();
      io.to(SOCKET_ROOMS.TENANT(tenantId)).emit(
        SOCKET_EVENTS.RECEIVE_NOTIFICATION,
        notification,
      );
      Logger.info(
        `Notification sent to tenant ${tenantId}:`,
        notification.type,
      );
    } catch (error) {
      Logger.error('Error sending notification to tenant:', error);
    }
  }

  // Handle private messaging
  async sendPrivateMessage(
    senderId: number,
    receiverId: number,
    message: ChatMessage,
  ) {
    try {
      const io = this.getIO();
      const roomId = SOCKET_ROOMS.PRIVATE_CHAT(senderId, receiverId);

      // Send message to both users
      io.to(SOCKET_ROOMS.USER(senderId)).emit(
        SOCKET_EVENTS.RECEIVE_MESSAGE,
        message,
      );
      io.to(SOCKET_ROOMS.USER(receiverId)).emit(
        SOCKET_EVENTS.RECEIVE_MESSAGE,
        message,
      );

      // Mark as delivered if receiver is online
      if (this.onlineUsers.has(receiverId)) {
        io.to(SOCKET_ROOMS.USER(senderId)).emit(
          SOCKET_EVENTS.MESSAGE_DELIVERED,
          {
            messageId: message.id,
            receiverId,
            deliveredAt: new Date(),
          },
        );
      }

      Logger.info(`Private message sent from ${senderId} to ${receiverId}`);
    } catch (error) {
      Logger.error('Error sending private message:', error);
    }
  }

  // Handle typing indicators
  handleTypingIndicator(indicator: TypingIndicator) {
    try {
      const io = this.getIO();

      if (indicator.receiverId) {
        // Private chat typing
        io.to(SOCKET_ROOMS.USER(indicator.receiverId)).emit(
          SOCKET_EVENTS.TYPING_INDICATOR,
          indicator,
        );
      } else if (indicator.roomId) {
        // Room/group chat typing
        io.to(indicator.roomId).emit(SOCKET_EVENTS.TYPING_INDICATOR, indicator);
      }
    } catch (error) {
      Logger.error('Error handling typing indicator:', error);
    }
  }

  // Handle follow requests
  async handleFollowRequest(followRequest: FollowRequest) {
    try {
      const notification: SocketNotification = {
        userId: followRequest.followingId,
        tenantId: followRequest.tenantId,
        type: 'follow_request' as any,
        title: 'New Follow Request',
        message: `${followRequest.followerName} wants to follow you`,
        data: { followRequest },
        createdAt: new Date(),
      };

      await this.sendNotificationToUser(
        followRequest.followingId,
        notification,
      );

      const io = this.getIO();
      io.to(SOCKET_ROOMS.USER(followRequest.followingId)).emit(
        SOCKET_EVENTS.FOLLOW_REQUEST,
        followRequest,
      );

      Logger.info(
        `Follow request sent from ${followRequest.followerId} to ${followRequest.followingId}`,
      );
    } catch (error) {
      Logger.error('Error handling follow request:', error);
    }
  }

  // Handle event updates
  async handleEventUpdate(eventUpdate: EventUpdate) {
    try {
      const io = this.getIO();

      // Send to tenant room
      io.to(SOCKET_ROOMS.TENANT(eventUpdate.tenantId)).emit(
        SOCKET_EVENTS.EVENT_UPDATED,
        eventUpdate,
      );

      // Send to specific event room if it exists
      io.to(SOCKET_ROOMS.EVENT(eventUpdate.eventId)).emit(
        SOCKET_EVENTS.EVENT_UPDATED,
        eventUpdate,
      );

      Logger.info(
        `Event update broadcasted for event ${eventUpdate.eventId}:`,
        eventUpdate.updateType,
      );
    } catch (error) {
      Logger.error('Error handling event update:', error);
    }
  }

  // Handle job updates
  async handleJobUpdate(jobUpdate: JobUpdate) {
    try {
      const io = this.getIO();

      // Send to tenant room
      io.to(SOCKET_ROOMS.TENANT(jobUpdate.tenantId)).emit(
        SOCKET_EVENTS.JOB_UPDATED,
        jobUpdate,
      );

      // Send to specific job room if it exists
      io.to(SOCKET_ROOMS.JOB(jobUpdate.jobId)).emit(
        SOCKET_EVENTS.JOB_UPDATED,
        jobUpdate,
      );

      Logger.info(
        `Job update broadcasted for job ${jobUpdate.jobId}:`,
        jobUpdate.updateType,
      );
    } catch (error) {
      Logger.error('Error handling job update:', error);
    }
  }

  // Handle post updates
  async handlePostUpdate(postUpdate: PostUpdate) {
    try {
      const io = this.getIO();

      // Send to tenant room
      io.to(SOCKET_ROOMS.TENANT(postUpdate.tenantId)).emit(
        SOCKET_EVENTS.POST_UPDATED,
        postUpdate,
      );

      // Send to specific post room if it exists
      io.to(SOCKET_ROOMS.POST(postUpdate.postId)).emit(
        SOCKET_EVENTS.POST_UPDATED,
        postUpdate,
      );

      Logger.info(
        `Post update broadcasted for post ${postUpdate.postId}:`,
        postUpdate.updateType,
      );
    } catch (error) {
      Logger.error('Error handling post update:', error);
    }
  }

  // Get online users for a tenant
  getOnlineUsersForTenant(tenantId: number): OnlineUser[] {
    return Array.from(this.onlineUsers.values()).filter(
      user => user.tenantId === tenantId,
    );
  }

  // Check if user is online
  isUserOnline(userId: number): boolean {
    return this.onlineUsers.has(userId);
  }

  // Get user's socket IDs
  getUserSocketIds(userId: number): string[] {
    const socketSet = this.userSockets.get(userId);
    return socketSet ? Array.from(socketSet) : [];
  }

  // Helper method to get unread notification count
  private async getUnreadNotificationCount(userId: number): Promise<number> {
    try {
      // This would typically query your notifications table
      // For now, returning a placeholder
      return 0;
    } catch (error) {
      Logger.error('Error getting unread notification count:', error);
      return 0;
    }
  }

  // Update user status
  async updateUserStatus(userId: number, status: UserStatus) {
    try {
      const onlineUser = this.onlineUsers.get(userId);
      if (onlineUser) {
        onlineUser.status = status;
        onlineUser.lastSeen = new Date();

        const io = this.getIO();
        io.to(SOCKET_ROOMS.TENANT(onlineUser.tenantId)).emit(
          SOCKET_EVENTS.USER_STATUS_CHANGE,
          {
            userId,
            status,
            lastSeen: onlineUser.lastSeen,
            timestamp: new Date(),
          },
        );

        Logger.info(`User ${userId} status updated to ${status}`);
      }
    } catch (error) {
      Logger.error('Error updating user status:', error);
    }
  }

  // Join room
  async joinRoom(
    socket: AuthenticatedSocket,
    roomId: string,
    roomType: string,
  ) {
    try {
      await socket.join(roomId);

      // Notify room about user joining (for certain room types)
      if (roomType === 'chat' || roomType === 'event') {
        socket.to(roomId).emit(SOCKET_EVENTS.USER_ONLINE, {
          userId: socket.userId,
          roomId,
          timestamp: new Date(),
        });
      }

      Logger.info(`User ${socket.userId} joined room ${roomId} (${roomType})`);
    } catch (error) {
      Logger.error('Error joining room:', error);
    }
  }

  // Leave room
  async leaveRoom(
    socket: AuthenticatedSocket,
    roomId: string,
    roomType: string,
  ) {
    try {
      await socket.leave(roomId);

      // Notify room about user leaving (for certain room types)
      if (roomType === 'chat' || roomType === 'event') {
        socket.to(roomId).emit(SOCKET_EVENTS.USER_OFFLINE, {
          userId: socket.userId,
          roomId,
          timestamp: new Date(),
        });
      }

      Logger.info(`User ${socket.userId} left room ${roomId} (${roomType})`);
    } catch (error) {
      Logger.error('Error leaving room:', error);
    }
  }

  // Broadcast to room
  async broadcastToRoom(
    roomId: string,
    event: string,
    data: any,
    excludeUserId?: number,
  ) {
    try {
      const io = this.getIO();

      if (excludeUserId) {
        // Exclude specific user from broadcast
        const userSocketIds = this.getUserSocketIds(excludeUserId);
        const sockets = await io.in(roomId).fetchSockets();

        sockets.forEach(socket => {
          if (!userSocketIds.includes(socket.id)) {
            socket.emit(event, data);
          }
        });
      } else {
        io.to(roomId).emit(event, data);
      }

      Logger.info(`Broadcasted ${event} to room ${roomId}`);
    } catch (error) {
      Logger.error('Error broadcasting to room:', error);
    }
  }

  // Get room members
  async getRoomMembers(roomId: string): Promise<number[]> {
    try {
      const io = this.getIO();
      const sockets = await io.in(roomId).fetchSockets();

      const userIds: number[] = [];
      sockets.forEach(socket => {
        const authSocket = socket as unknown as AuthenticatedSocket;
        if (authSocket.userId) {
          userIds.push(authSocket.userId);
        }
      });

      return [...new Set(userIds)]; // Remove duplicates
    } catch (error) {
      Logger.error('Error getting room members:', error);
      return [];
    }
  }

  // Cleanup method
  cleanup() {
    this.onlineUsers.clear();
    this.userSockets.clear();
    this.typingUsers.clear();
    Logger.info('SocketService cleaned up');
  }
}

// Export singleton instance
export const socketService = new SocketService();
export default socketService;
